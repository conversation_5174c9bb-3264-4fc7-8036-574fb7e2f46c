/*! For license information please see async-markdown.js.LICENSE.txt */
(self.webpackChunkdash_core_components=self.webpackChunkdash_core_components||[]).push([[754],{54679:function(t,e,r){"use strict";r.r(e),r.d(e,{default:function(){return T}});var i=r(99196),n=r.n(i),s=r(84214),a=r(83923),o=r.n(a),c=r(94838),h=r.n(c),u=r(3933),l=r.n(u),p=r(69064),f=r.n(p),d=r(20748);function m(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(n=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,"string");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(i.key))?n:String(n)),i)}var n}function g(t,e){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},g(t,e)}function v(t){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},v(t)}var x=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&g(t,e)}(o,t);var e,r,i,s,a=(i=o,s=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=v(i);if(s){var r=v(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function o(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),(e=a.call(this,t)).span_element=n().createRef(),e}return e=o,(r=[{key:"componentDidMount",value:function(){this.renderMath()}},{key:"componentDidUpdate",value:function(t){t.tex===this.props.tex&&t.inline===this.props.inline||this.renderMath()}},{key:"renderMath",value:function(){var t=this.span_element.current;(0,d.Z)().then((function(){window.MathJax.typeset([t])}))}},{key:"render",value:function(){return n().createElement("span",{ref:this.span_element},this.props.inline?"\\(":"\\[",this.props.tex,this.props.inline?"\\)":"\\]")}}])&&m(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),o}(i.Component);x.propTypes={tex:f().string,inline:f().bool},x.defaultProps={tex:"",inline:!0},x.propTypes,x.defaultProps;var y={loadhljs:function(){var t=this;return Promise.resolve(window.hljs||r.e(790).then(r.bind(r,73266)).then((function(t){return t.default}))).then((function(e){t.hljs=e,t.hljsResolve(),t.isReady=!0}))}},b=new Promise((function(t){y.hljsResolve=t}));y.isReady=b;var k=y,w=r(48786),_=r(16111);function A(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}function C(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(n=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,"string");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(i.key))?n:String(n)),i)}var n}function E(t,e){return E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},E(t,e)}function S(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function P(t){return P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},P(t)}var T=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&E(t,e)}(u,t);var e,r,i,a,c=(i=u,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=P(i);if(a){var r=P(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return S(t)}(this,t)});function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),e=c.call(this,t),!0!==k.isReady&&k.isReady.then((function(){e.setState({})})),e.highlightCode=e.highlightCode.bind(S(e)),e.dedent=e.dedent.bind(S(e)),e}return e=u,r=[{key:"componentDidMount",value:function(){this.highlightCode()}},{key:"componentDidUpdate",value:function(){this.highlightCode()}},{key:"highlightCode",value:function(){if(this.mdContainer){var t=this.mdContainer.querySelectorAll("pre code");if(k.hljs)for(var e=0;e<t.length;e++)k.hljs.highlightElement(t[e]);else k.loadhljs()}}},{key:"dedent",value:function(t){var e,r=t.split(/\r\n|\r|\n/),i=null,n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return A(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?A(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){o=!0,s=t},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}(r);try{for(n.s();!(e=n.n()).done;){var s=e.value,a=s&&s.match(/^\s*(?=\S)/);if(a){var o=a[0];if(null!==i){for(var c=0;c<i.length;c++)if(o[c]!==i[c]){i=i.substr(0,c);break}}else i=o;if(!i)break}}}catch(t){n.e(t)}finally{n.f()}var h=i?i.length:0;return r.map((function(t){return t.match(/\S/)?t.substr(h):""})).join("\n")}},{key:"render",value:function(){var t=this,e=this.props,r=e.id,i=e.style,a=e.className,c=e.highlight_config,u=e.loading_state,p=e.dangerously_allow_html,f=e.link_target,d=e.mathjax,m=e.children,g=e.dedent,v="Array"===(0,s.dt8)(m)?m.join("\n"):m,y=g&&v?this.dedent(v):v,b={dccLink:function(t){return n().createElement(_.Z,t)},dccMarkdown:function(e){return n().createElement(h(),(0,s.BPw)((0,s.eiS)(["dangerously_allow_html","dedent"],t.props),(0,s.eiS)(["children"],e)))},dashMathjax:function(t){return n().createElement(x,{tex:t.value,inline:t.inline})}};return n().createElement("div",{id:r,ref:function(e){t.mdContainer=e},style:i,className:(c&&c.theme||a)&&"".concat(a||""," ").concat(c&&c.theme&&"dark"===c.theme?"hljs-dark":""),"data-dash-is-loading":u&&u.is_loading||void 0},n().createElement(h(),{source:y,escapeHtml:!p,linkTarget:f,plugins:d?[l()]:[],renderers:{math:function(t){return n().createElement(x,{tex:t.value,inline:!1})},inlineMath:function(t){return n().createElement(x,{tex:t.value,inline:!0})},html:function(t){return t.escapeHtml?t.value:n().createElement(o(),{jsx:d?(e=t.value,e.replace(/(\${1,2})((?:\\.|[^$])+)\1/g,(function(t,e,r){var i=1===e.length||-1===r.indexOf("\n");return"<dashMathjax value='".concat(r,"' inline='").concat(i,"'/>")}))):t.value,components:b,renderInWrapper:!1});var e}}}))}}],r&&C(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),u}(i.Component);T.propTypes=w.iG,T.defaultProps=w.lG},2580:function(t){"use strict";var e=Object.prototype.toString;t.exports=function(t){var r;return"[object Object]"===e.call(t)&&(null===(r=Object.getPrototypeOf(t))||r===Object.getPrototypeOf({}))}},83923:function(t,e,r){var i,n=r(25108);function s(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(n=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,"string");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(i.key))?n:String(n)),i)}var n}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var i=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(i){var n=Object.getOwnPropertyDescriptor(i,e);return n.get?n.get.call(arguments.length<3?t:r):n.value}},a.apply(this,arguments)}function o(t,e){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},o(t,e)}function c(t){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},c(t)}t.exports=(i=r(99196),function(t){var e={};function r(i){if(e[i])return e[i].exports;var n=e[i]={i:i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=t,r.c=e,r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(i,n,function(e){return t[e]}.bind(null,n));return i},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=4)}([function(t,e){t.exports=i},function(t,e,r){"use strict";r.r(e),r.d(e,"Node",(function(){return rt})),r.d(e,"Parser",(function(){return D})),r.d(e,"Position",(function(){return j})),r.d(e,"SourceLocation",(function(){return V})),r.d(e,"TokContext",(function(){return st})),r.d(e,"Token",(function(){return Et})),r.d(e,"TokenType",(function(){return g})),r.d(e,"defaultOptions",(function(){return B})),r.d(e,"getLineInfo",(function(){return R})),r.d(e,"isIdentifierChar",(function(){return m})),r.d(e,"isIdentifierStart",(function(){return d})),r.d(e,"isNewLine",(function(){return C})),r.d(e,"keywordTypes",(function(){return b})),r.d(e,"lineBreak",(function(){return _})),r.d(e,"lineBreakG",(function(){return A})),r.d(e,"nonASCIIwhitespace",(function(){return E})),r.d(e,"parse",(function(){return Ot})),r.d(e,"parseExpressionAt",(function(){return Nt})),r.d(e,"tokContexts",(function(){return at})),r.d(e,"tokTypes",(function(){return w})),r.d(e,"tokenizer",(function(){return Lt})),r.d(e,"version",(function(){return It}));var i={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},n="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",s={5:n,"5module":n+" export import",6:n+" const class extends export import super"},a=/^in(stanceof)?$/,o="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-Ᶎꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭧꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",c="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ංඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",h=new RegExp("["+o+"]"),u=new RegExp("["+o+c+"]");o=c=null;var l=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,477,28,11,0,9,21,155,22,13,52,76,44,33,24,27,35,30,0,12,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,0,33,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,0,161,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,270,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,754,9486,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,15,7472,3104,541],p=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,525,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,4,9,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,232,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,792487,239];function f(t,e){for(var r=65536,i=0;i<e.length;i+=2){if((r+=e[i])>t)return!1;if((r+=e[i+1])>=t)return!0}}function d(t,e){return t<65?36===t:t<91||(t<97?95===t:t<123||(t<=65535?t>=170&&h.test(String.fromCharCode(t)):!1!==e&&f(t,l)))}function m(t,e){return t<48?36===t:t<58||!(t<65)&&(t<91||(t<97?95===t:t<123||(t<=65535?t>=170&&u.test(String.fromCharCode(t)):!1!==e&&(f(t,l)||f(t,p)))))}var g=function(t,e){void 0===e&&(e={}),this.label=t,this.keyword=e.keyword,this.beforeExpr=!!e.beforeExpr,this.startsExpr=!!e.startsExpr,this.isLoop=!!e.isLoop,this.isAssign=!!e.isAssign,this.prefix=!!e.prefix,this.postfix=!!e.postfix,this.binop=e.binop||null,this.updateContext=null};function v(t,e){return new g(t,{beforeExpr:!0,binop:e})}var x={beforeExpr:!0},y={startsExpr:!0},b={};function k(t,e){return void 0===e&&(e={}),e.keyword=t,b[t]=new g(t,e)}var w={num:new g("num",y),regexp:new g("regexp",y),string:new g("string",y),name:new g("name",y),eof:new g("eof"),bracketL:new g("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new g("]"),braceL:new g("{",{beforeExpr:!0,startsExpr:!0}),braceR:new g("}"),parenL:new g("(",{beforeExpr:!0,startsExpr:!0}),parenR:new g(")"),comma:new g(",",x),semi:new g(";",x),colon:new g(":",x),dot:new g("."),question:new g("?",x),arrow:new g("=>",x),template:new g("template"),invalidTemplate:new g("invalidTemplate"),ellipsis:new g("...",x),backQuote:new g("`",y),dollarBraceL:new g("${",{beforeExpr:!0,startsExpr:!0}),eq:new g("=",{beforeExpr:!0,isAssign:!0}),assign:new g("_=",{beforeExpr:!0,isAssign:!0}),incDec:new g("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new g("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:v("||",1),logicalAND:v("&&",2),bitwiseOR:v("|",3),bitwiseXOR:v("^",4),bitwiseAND:v("&",5),equality:v("==/!=/===/!==",6),relational:v("</>/<=/>=",7),bitShift:v("<</>>/>>>",8),plusMin:new g("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:v("%",10),star:v("*",10),slash:v("/",10),starstar:new g("**",{beforeExpr:!0}),_break:k("break"),_case:k("case",x),_catch:k("catch"),_continue:k("continue"),_debugger:k("debugger"),_default:k("default",x),_do:k("do",{isLoop:!0,beforeExpr:!0}),_else:k("else",x),_finally:k("finally"),_for:k("for",{isLoop:!0}),_function:k("function",y),_if:k("if"),_return:k("return",x),_switch:k("switch"),_throw:k("throw",x),_try:k("try"),_var:k("var"),_const:k("const"),_while:k("while",{isLoop:!0}),_with:k("with"),_new:k("new",{beforeExpr:!0,startsExpr:!0}),_this:k("this",y),_super:k("super",y),_class:k("class",y),_extends:k("extends",x),_export:k("export"),_import:k("import",y),_null:k("null",y),_true:k("true",y),_false:k("false",y),_in:k("in",{beforeExpr:!0,binop:7}),_instanceof:k("instanceof",{beforeExpr:!0,binop:7}),_typeof:k("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:k("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:k("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},_=/\r\n?|\n|\u2028|\u2029/,A=new RegExp(_.source,"g");function C(t,e){return 10===t||13===t||!e&&(8232===t||8233===t)}var E=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,S=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,P=Object.prototype,T=P.hasOwnProperty,I=P.toString;function O(t,e){return T.call(t,e)}var N=Array.isArray||function(t){return"[object Array]"===I.call(t)};function L(t){return new RegExp("^(?:"+t.replace(/ /g,"|")+")$")}var j=function(t,e){this.line=t,this.column=e};j.prototype.offset=function(t){return new j(this.line,this.column+t)};var V=function(t,e,r){this.start=e,this.end=r,null!==t.sourceFile&&(this.source=t.sourceFile)};function R(t,e){for(var r=1,i=0;;){A.lastIndex=i;var n=A.exec(t);if(!(n&&n.index<e))return new j(r,e-i);++r,i=n.index+n[0].length}}var B={ecmaVersion:10,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1};function M(t,e){return 2|(t?4:0)|(e?8:0)}var D=function(t,e,r){this.options=t=function(t){var e={};for(var r in B)e[r]=t&&O(t,r)?t[r]:B[r];if(e.ecmaVersion>=2015&&(e.ecmaVersion-=2009),null==e.allowReserved&&(e.allowReserved=e.ecmaVersion<5),N(e.onToken)){var i=e.onToken;e.onToken=function(t){return i.push(t)}}return N(e.onComment)&&(e.onComment=function(t,e){return function(r,i,n,s,a,o){var c={type:r?"Block":"Line",value:i,start:n,end:s};t.locations&&(c.loc=new V(this,a,o)),t.ranges&&(c.range=[n,s]),e.push(c)}}(e,e.onComment)),e}(t),this.sourceFile=t.sourceFile,this.keywords=L(s[t.ecmaVersion>=6?6:"module"===t.sourceType?"5module":5]);var n="";if(!0!==t.allowReserved){for(var a=t.ecmaVersion;!(n=i[a]);a--);"module"===t.sourceType&&(n+=" await")}this.reservedWords=L(n);var o=(n?n+" ":"")+i.strict;this.reservedWordsStrict=L(o),this.reservedWordsStrictBind=L(o+" "+i.strictBind),this.input=String(e),this.containsEsc=!1,r?(this.pos=r,this.lineStart=this.input.lastIndexOf("\n",r-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(_).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=w.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===t.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports={},0===this.pos&&t.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null},U={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0}};D.prototype.parse=function(){var t=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(t)},U.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},U.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0},U.inAsync.get=function(){return(4&this.currentVarScope().flags)>0},U.allowSuper.get=function(){return(64&this.currentThisScope().flags)>0},U.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},U.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},D.prototype.inNonArrowFunction=function(){return(2&this.currentThisScope().flags)>0},D.extend=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];for(var r=this,i=0;i<t.length;i++)r=t[i](r);return r},D.parse=function(t,e){return new this(e,t).parse()},D.parseExpressionAt=function(t,e,r){var i=new this(r,t,e);return i.nextToken(),i.parseExpression()},D.tokenizer=function(t,e){return new this(e,t)},Object.defineProperties(D.prototype,U);var H=D.prototype,F=/^(?:'((?:\\.|[^'])*?)'|"((?:\\.|[^"])*?)")/;function z(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}H.strictDirective=function(t){for(;;){S.lastIndex=t,t+=S.exec(this.input)[0].length;var e=F.exec(this.input.slice(t));if(!e)return!1;if("use strict"===(e[1]||e[2]))return!0;t+=e[0].length,S.lastIndex=t,t+=S.exec(this.input)[0].length,";"===this.input[t]&&t++}},H.eat=function(t){return this.type===t&&(this.next(),!0)},H.isContextual=function(t){return this.type===w.name&&this.value===t&&!this.containsEsc},H.eatContextual=function(t){return!!this.isContextual(t)&&(this.next(),!0)},H.expectContextual=function(t){this.eatContextual(t)||this.unexpected()},H.canInsertSemicolon=function(){return this.type===w.eof||this.type===w.braceR||_.test(this.input.slice(this.lastTokEnd,this.start))},H.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},H.semicolon=function(){this.eat(w.semi)||this.insertSemicolon()||this.unexpected()},H.afterTrailingComma=function(t,e){if(this.type===t)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),e||this.next(),!0},H.expect=function(t){this.eat(t)||this.unexpected()},H.unexpected=function(t){this.raise(null!=t?t:this.start,"Unexpected token")},H.checkPatternErrors=function(t,e){if(t){t.trailingComma>-1&&this.raiseRecoverable(t.trailingComma,"Comma is not permitted after the rest element");var r=e?t.parenthesizedAssign:t.parenthesizedBind;r>-1&&this.raiseRecoverable(r,"Parenthesized pattern")}},H.checkExpressionErrors=function(t,e){if(!t)return!1;var r=t.shorthandAssign,i=t.doubleProto;if(!e)return r>=0||i>=0;r>=0&&this.raise(r,"Shorthand property assignments are valid only in destructuring patterns"),i>=0&&this.raiseRecoverable(i,"Redefinition of __proto__ property")},H.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},H.isSimpleAssignTarget=function(t){return"ParenthesizedExpression"===t.type?this.isSimpleAssignTarget(t.expression):"Identifier"===t.type||"MemberExpression"===t.type};var q=D.prototype;q.parseTopLevel=function(t){var e={};for(t.body||(t.body=[]);this.type!==w.eof;){var r=this.parseStatement(null,!0,e);t.body.push(r)}if(this.inModule)for(var i=0,n=Object.keys(this.undefinedExports);i<n.length;i+=1){var s=n[i];this.raiseRecoverable(this.undefinedExports[s].start,"Export '"+s+"' is not defined")}return this.adaptDirectivePrologue(t.body),this.next(),t.sourceType=this.options.sourceType,this.finishNode(t,"Program")};var G={kind:"loop"},W={kind:"switch"};q.isLet=function(t){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;S.lastIndex=this.pos;var e=S.exec(this.input),r=this.pos+e[0].length,i=this.input.charCodeAt(r);if(91===i)return!0;if(t)return!1;if(123===i)return!0;if(d(i,!0)){for(var n=r+1;m(this.input.charCodeAt(n),!0);)++n;var s=this.input.slice(r,n);if(!a.test(s))return!0}return!1},q.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;S.lastIndex=this.pos;var t=S.exec(this.input),e=this.pos+t[0].length;return!(_.test(this.input.slice(this.pos,e))||"function"!==this.input.slice(e,e+8)||e+8!==this.input.length&&m(this.input.charAt(e+8)))},q.parseStatement=function(t,e,r){var i,n=this.type,s=this.startNode();switch(this.isLet(t)&&(n=w._var,i="let"),n){case w._break:case w._continue:return this.parseBreakContinueStatement(s,n.keyword);case w._debugger:return this.parseDebuggerStatement(s);case w._do:return this.parseDoStatement(s);case w._for:return this.parseForStatement(s);case w._function:return t&&(this.strict||"if"!==t&&"label"!==t)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(s,!1,!t);case w._class:return t&&this.unexpected(),this.parseClass(s,!0);case w._if:return this.parseIfStatement(s);case w._return:return this.parseReturnStatement(s);case w._switch:return this.parseSwitchStatement(s);case w._throw:return this.parseThrowStatement(s);case w._try:return this.parseTryStatement(s);case w._const:case w._var:return i=i||this.value,t&&"var"!==i&&this.unexpected(),this.parseVarStatement(s,i);case w._while:return this.parseWhileStatement(s);case w._with:return this.parseWithStatement(s);case w.braceL:return this.parseBlock(!0,s);case w.semi:return this.parseEmptyStatement(s);case w._export:case w._import:if(this.options.ecmaVersion>10&&n===w._import){S.lastIndex=this.pos;var a=S.exec(this.input),o=this.pos+a[0].length;if(40===this.input.charCodeAt(o))return this.parseExpressionStatement(s,this.parseExpression())}return this.options.allowImportExportEverywhere||(e||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),n===w._import?this.parseImport(s):this.parseExport(s,r);default:if(this.isAsyncFunction())return t&&this.unexpected(),this.next(),this.parseFunctionStatement(s,!0,!t);var c=this.value,h=this.parseExpression();return n===w.name&&"Identifier"===h.type&&this.eat(w.colon)?this.parseLabeledStatement(s,c,h,t):this.parseExpressionStatement(s,h)}},q.parseBreakContinueStatement=function(t,e){var r="break"===e;this.next(),this.eat(w.semi)||this.insertSemicolon()?t.label=null:this.type!==w.name?this.unexpected():(t.label=this.parseIdent(),this.semicolon());for(var i=0;i<this.labels.length;++i){var n=this.labels[i];if(null==t.label||n.name===t.label.name){if(null!=n.kind&&(r||"loop"===n.kind))break;if(t.label&&r)break}}return i===this.labels.length&&this.raise(t.start,"Unsyntactic "+e),this.finishNode(t,r?"BreakStatement":"ContinueStatement")},q.parseDebuggerStatement=function(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")},q.parseDoStatement=function(t){return this.next(),this.labels.push(G),t.body=this.parseStatement("do"),this.labels.pop(),this.expect(w._while),t.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(w.semi):this.semicolon(),this.finishNode(t,"DoWhileStatement")},q.parseForStatement=function(t){this.next();var e=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(G),this.enterScope(0),this.expect(w.parenL),this.type===w.semi)return e>-1&&this.unexpected(e),this.parseFor(t,null);var r=this.isLet();if(this.type===w._var||this.type===w._const||r){var i=this.startNode(),n=r?"let":this.value;return this.next(),this.parseVar(i,!0,n),this.finishNode(i,"VariableDeclaration"),(this.type===w._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===i.declarations.length?(this.options.ecmaVersion>=9&&(this.type===w._in?e>-1&&this.unexpected(e):t.await=e>-1),this.parseForIn(t,i)):(e>-1&&this.unexpected(e),this.parseFor(t,i))}var s=new z,a=this.parseExpression(!0,s);return this.type===w._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.options.ecmaVersion>=9&&(this.type===w._in?e>-1&&this.unexpected(e):t.await=e>-1),this.toAssignable(a,!1,s),this.checkLVal(a),this.parseForIn(t,a)):(this.checkExpressionErrors(s,!0),e>-1&&this.unexpected(e),this.parseFor(t,a))},q.parseFunctionStatement=function(t,e,r){return this.next(),this.parseFunction(t,J|(r?0:K),!1,e)},q.parseIfStatement=function(t){return this.next(),t.test=this.parseParenExpression(),t.consequent=this.parseStatement("if"),t.alternate=this.eat(w._else)?this.parseStatement("if"):null,this.finishNode(t,"IfStatement")},q.parseReturnStatement=function(t){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(w.semi)||this.insertSemicolon()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")},q.parseSwitchStatement=function(t){var e;this.next(),t.discriminant=this.parseParenExpression(),t.cases=[],this.expect(w.braceL),this.labels.push(W),this.enterScope(0);for(var r=!1;this.type!==w.braceR;)if(this.type===w._case||this.type===w._default){var i=this.type===w._case;e&&this.finishNode(e,"SwitchCase"),t.cases.push(e=this.startNode()),e.consequent=[],this.next(),i?e.test=this.parseExpression():(r&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),r=!0,e.test=null),this.expect(w.colon)}else e||this.unexpected(),e.consequent.push(this.parseStatement(null));return this.exitScope(),e&&this.finishNode(e,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(t,"SwitchStatement")},q.parseThrowStatement=function(t){return this.next(),_.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")};var X=[];q.parseTryStatement=function(t){if(this.next(),t.block=this.parseBlock(),t.handler=null,this.type===w._catch){var e=this.startNode();if(this.next(),this.eat(w.parenL)){e.param=this.parseBindingAtom();var r="Identifier"===e.param.type;this.enterScope(r?32:0),this.checkLVal(e.param,r?4:2),this.expect(w.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),e.param=null,this.enterScope(0);e.body=this.parseBlock(!1),this.exitScope(),t.handler=this.finishNode(e,"CatchClause")}return t.finalizer=this.eat(w._finally)?this.parseBlock():null,t.handler||t.finalizer||this.raise(t.start,"Missing catch or finally clause"),this.finishNode(t,"TryStatement")},q.parseVarStatement=function(t,e){return this.next(),this.parseVar(t,!1,e),this.semicolon(),this.finishNode(t,"VariableDeclaration")},q.parseWhileStatement=function(t){return this.next(),t.test=this.parseParenExpression(),this.labels.push(G),t.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(t,"WhileStatement")},q.parseWithStatement=function(t){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),t.object=this.parseParenExpression(),t.body=this.parseStatement("with"),this.finishNode(t,"WithStatement")},q.parseEmptyStatement=function(t){return this.next(),this.finishNode(t,"EmptyStatement")},q.parseLabeledStatement=function(t,e,r,i){for(var n=0,s=this.labels;n<s.length;n+=1)s[n].name===e&&this.raise(r.start,"Label '"+e+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===w._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var c=this.labels[o];if(c.statementStart!==t.start)break;c.statementStart=this.start,c.kind=a}return this.labels.push({name:e,kind:a,statementStart:this.start}),t.body=this.parseStatement(i?-1===i.indexOf("label")?i+"label":i:"label"),this.labels.pop(),t.label=r,this.finishNode(t,"LabeledStatement")},q.parseExpressionStatement=function(t,e){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")},q.parseBlock=function(t,e){for(void 0===t&&(t=!0),void 0===e&&(e=this.startNode()),e.body=[],this.expect(w.braceL),t&&this.enterScope(0);!this.eat(w.braceR);){var r=this.parseStatement(null);e.body.push(r)}return t&&this.exitScope(),this.finishNode(e,"BlockStatement")},q.parseFor=function(t,e){return t.init=e,this.expect(w.semi),t.test=this.type===w.semi?null:this.parseExpression(),this.expect(w.semi),t.update=this.type===w.parenR?null:this.parseExpression(),this.expect(w.parenR),t.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(t,"ForStatement")},q.parseForIn=function(t,e){var r=this.type===w._in;return this.next(),"VariableDeclaration"===e.type&&null!=e.declarations[0].init&&(!r||this.options.ecmaVersion<8||this.strict||"var"!==e.kind||"Identifier"!==e.declarations[0].id.type)?this.raise(e.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer"):"AssignmentPattern"===e.type&&this.raise(e.start,"Invalid left-hand side in for-loop"),t.left=e,t.right=r?this.parseExpression():this.parseMaybeAssign(),this.expect(w.parenR),t.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(t,r?"ForInStatement":"ForOfStatement")},q.parseVar=function(t,e,r){for(t.declarations=[],t.kind=r;;){var i=this.startNode();if(this.parseVarId(i,r),this.eat(w.eq)?i.init=this.parseMaybeAssign(e):"const"!==r||this.type===w._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===i.id.type||e&&(this.type===w._in||this.isContextual("of"))?i.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),t.declarations.push(this.finishNode(i,"VariableDeclarator")),!this.eat(w.comma))break}return t},q.parseVarId=function(t,e){t.id=this.parseBindingAtom(),this.checkLVal(t.id,"var"===e?1:2,!1)};var J=1,K=2;q.parseFunction=function(t,e,r,i){this.initFunction(t),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!i)&&(this.type===w.star&&e&K&&this.unexpected(),t.generator=this.eat(w.star)),this.options.ecmaVersion>=8&&(t.async=!!i),e&J&&(t.id=4&e&&this.type!==w.name?null:this.parseIdent(),!t.id||e&K||this.checkLVal(t.id,this.strict||t.generator||t.async?this.treatFunctionsAsVar?1:2:3));var n=this.yieldPos,s=this.awaitPos,a=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(M(t.async,t.generator)),e&J||(t.id=this.type===w.name?this.parseIdent():null),this.parseFunctionParams(t),this.parseFunctionBody(t,r,!1),this.yieldPos=n,this.awaitPos=s,this.awaitIdentPos=a,this.finishNode(t,e&J?"FunctionDeclaration":"FunctionExpression")},q.parseFunctionParams=function(t){this.expect(w.parenL),t.params=this.parseBindingList(w.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},q.parseClass=function(t,e){this.next();var r=this.strict;this.strict=!0,this.parseClassId(t,e),this.parseClassSuper(t);var i=this.startNode(),n=!1;for(i.body=[],this.expect(w.braceL);!this.eat(w.braceR);){var s=this.parseClassElement(null!==t.superClass);s&&(i.body.push(s),"MethodDefinition"===s.type&&"constructor"===s.kind&&(n&&this.raise(s.start,"Duplicate constructor in the same class"),n=!0))}return t.body=this.finishNode(i,"ClassBody"),this.strict=r,this.finishNode(t,e?"ClassDeclaration":"ClassExpression")},q.parseClassElement=function(t){var e=this;if(this.eat(w.semi))return null;var r=this.startNode(),i=function(t,i){void 0===i&&(i=!1);var n=e.start,s=e.startLoc;return!(!e.eatContextual(t)||(e.type===w.parenL||i&&e.canInsertSemicolon())&&(r.key&&e.unexpected(),r.computed=!1,r.key=e.startNodeAt(n,s),r.key.name=t,e.finishNode(r.key,"Identifier"),1))};r.kind="method",r.static=i("static");var n=this.eat(w.star),s=!1;n||(this.options.ecmaVersion>=8&&i("async",!0)?(s=!0,n=this.options.ecmaVersion>=9&&this.eat(w.star)):i("get")?r.kind="get":i("set")&&(r.kind="set")),r.key||this.parsePropertyName(r);var a=r.key,o=!1;return r.computed||r.static||!("Identifier"===a.type&&"constructor"===a.name||"Literal"===a.type&&"constructor"===a.value)?r.static&&"Identifier"===a.type&&"prototype"===a.name&&this.raise(a.start,"Classes may not have a static property named prototype"):("method"!==r.kind&&this.raise(a.start,"Constructor can't have get/set modifier"),n&&this.raise(a.start,"Constructor can't be a generator"),s&&this.raise(a.start,"Constructor can't be an async method"),r.kind="constructor",o=t),this.parseClassMethod(r,n,s,o),"get"===r.kind&&0!==r.value.params.length&&this.raiseRecoverable(r.value.start,"getter should have no params"),"set"===r.kind&&1!==r.value.params.length&&this.raiseRecoverable(r.value.start,"setter should have exactly one param"),"set"===r.kind&&"RestElement"===r.value.params[0].type&&this.raiseRecoverable(r.value.params[0].start,"Setter cannot use rest params"),r},q.parseClassMethod=function(t,e,r,i){return t.value=this.parseMethod(e,r,i),this.finishNode(t,"MethodDefinition")},q.parseClassId=function(t,e){this.type===w.name?(t.id=this.parseIdent(),e&&this.checkLVal(t.id,2,!1)):(!0===e&&this.unexpected(),t.id=null)},q.parseClassSuper=function(t){t.superClass=this.eat(w._extends)?this.parseExprSubscripts():null},q.parseExport=function(t,e){if(this.next(),this.eat(w.star))return this.expectContextual("from"),this.type!==w.string&&this.unexpected(),t.source=this.parseExprAtom(),this.semicolon(),this.finishNode(t,"ExportAllDeclaration");if(this.eat(w._default)){var r;if(this.checkExport(e,"default",this.lastTokStart),this.type===w._function||(r=this.isAsyncFunction())){var i=this.startNode();this.next(),r&&this.next(),t.declaration=this.parseFunction(i,4|J,!1,r)}else if(this.type===w._class){var n=this.startNode();t.declaration=this.parseClass(n,"nullableID")}else t.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(t,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())t.declaration=this.parseStatement(null),"VariableDeclaration"===t.declaration.type?this.checkVariableExport(e,t.declaration.declarations):this.checkExport(e,t.declaration.id.name,t.declaration.id.start),t.specifiers=[],t.source=null;else{if(t.declaration=null,t.specifiers=this.parseExportSpecifiers(e),this.eatContextual("from"))this.type!==w.string&&this.unexpected(),t.source=this.parseExprAtom();else{for(var s=0,a=t.specifiers;s<a.length;s+=1){var o=a[s];this.checkUnreserved(o.local),this.checkLocalExport(o.local)}t.source=null}this.semicolon()}return this.finishNode(t,"ExportNamedDeclaration")},q.checkExport=function(t,e,r){t&&(O(t,e)&&this.raiseRecoverable(r,"Duplicate export '"+e+"'"),t[e]=!0)},q.checkPatternExport=function(t,e){var r=e.type;if("Identifier"===r)this.checkExport(t,e.name,e.start);else if("ObjectPattern"===r)for(var i=0,n=e.properties;i<n.length;i+=1){var s=n[i];this.checkPatternExport(t,s)}else if("ArrayPattern"===r)for(var a=0,o=e.elements;a<o.length;a+=1){var c=o[a];c&&this.checkPatternExport(t,c)}else"Property"===r?this.checkPatternExport(t,e.value):"AssignmentPattern"===r?this.checkPatternExport(t,e.left):"RestElement"===r?this.checkPatternExport(t,e.argument):"ParenthesizedExpression"===r&&this.checkPatternExport(t,e.expression)},q.checkVariableExport=function(t,e){if(t)for(var r=0,i=e;r<i.length;r+=1){var n=i[r];this.checkPatternExport(t,n.id)}},q.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},q.parseExportSpecifiers=function(t){var e=[],r=!0;for(this.expect(w.braceL);!this.eat(w.braceR);){if(r)r=!1;else if(this.expect(w.comma),this.afterTrailingComma(w.braceR))break;var i=this.startNode();i.local=this.parseIdent(!0),i.exported=this.eatContextual("as")?this.parseIdent(!0):i.local,this.checkExport(t,i.exported.name,i.exported.start),e.push(this.finishNode(i,"ExportSpecifier"))}return e},q.parseImport=function(t){return this.next(),this.type===w.string?(t.specifiers=X,t.source=this.parseExprAtom()):(t.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),t.source=this.type===w.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(t,"ImportDeclaration")},q.parseImportSpecifiers=function(){var t=[],e=!0;if(this.type===w.name){var r=this.startNode();if(r.local=this.parseIdent(),this.checkLVal(r.local,2),t.push(this.finishNode(r,"ImportDefaultSpecifier")),!this.eat(w.comma))return t}if(this.type===w.star){var i=this.startNode();return this.next(),this.expectContextual("as"),i.local=this.parseIdent(),this.checkLVal(i.local,2),t.push(this.finishNode(i,"ImportNamespaceSpecifier")),t}for(this.expect(w.braceL);!this.eat(w.braceR);){if(e)e=!1;else if(this.expect(w.comma),this.afterTrailingComma(w.braceR))break;var n=this.startNode();n.imported=this.parseIdent(!0),this.eatContextual("as")?n.local=this.parseIdent():(this.checkUnreserved(n.imported),n.local=n.imported),this.checkLVal(n.local,2),t.push(this.finishNode(n,"ImportSpecifier"))}return t},q.adaptDirectivePrologue=function(t){for(var e=0;e<t.length&&this.isDirectiveCandidate(t[e]);++e)t[e].directive=t[e].expression.raw.slice(1,-1)},q.isDirectiveCandidate=function(t){return"ExpressionStatement"===t.type&&"Literal"===t.expression.type&&"string"==typeof t.expression.value&&('"'===this.input[t.start]||"'"===this.input[t.start])};var $=D.prototype;$.toAssignable=function(t,e,r){if(this.options.ecmaVersion>=6&&t)switch(t.type){case"Identifier":this.inAsync&&"await"===t.name&&this.raise(t.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"RestElement":break;case"ObjectExpression":t.type="ObjectPattern",r&&this.checkPatternErrors(r,!0);for(var i=0,n=t.properties;i<n.length;i+=1){var s=n[i];this.toAssignable(s,e),"RestElement"!==s.type||"ArrayPattern"!==s.argument.type&&"ObjectPattern"!==s.argument.type||this.raise(s.argument.start,"Unexpected token")}break;case"Property":"init"!==t.kind&&this.raise(t.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(t.value,e);break;case"ArrayExpression":t.type="ArrayPattern",r&&this.checkPatternErrors(r,!0),this.toAssignableList(t.elements,e);break;case"SpreadElement":t.type="RestElement",this.toAssignable(t.argument,e),"AssignmentPattern"===t.argument.type&&this.raise(t.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==t.operator&&this.raise(t.left.end,"Only '=' operator can be used for specifying default value."),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,e);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(t.expression,e,r);break;case"MemberExpression":if(!e)break;default:this.raise(t.start,"Assigning to rvalue")}else r&&this.checkPatternErrors(r,!0);return t},$.toAssignableList=function(t,e){for(var r=t.length,i=0;i<r;i++){var n=t[i];n&&this.toAssignable(n,e)}if(r){var s=t[r-1];6===this.options.ecmaVersion&&e&&s&&"RestElement"===s.type&&"Identifier"!==s.argument.type&&this.unexpected(s.argument.start)}return t},$.parseSpread=function(t){var e=this.startNode();return this.next(),e.argument=this.parseMaybeAssign(!1,t),this.finishNode(e,"SpreadElement")},$.parseRestBinding=function(){var t=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==w.name&&this.unexpected(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")},$.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case w.bracketL:var t=this.startNode();return this.next(),t.elements=this.parseBindingList(w.bracketR,!0,!0),this.finishNode(t,"ArrayPattern");case w.braceL:return this.parseObj(!0)}return this.parseIdent()},$.parseBindingList=function(t,e,r){for(var i=[],n=!0;!this.eat(t);)if(n?n=!1:this.expect(w.comma),e&&this.type===w.comma)i.push(null);else{if(r&&this.afterTrailingComma(t))break;if(this.type===w.ellipsis){var s=this.parseRestBinding();this.parseBindingListItem(s),i.push(s),this.type===w.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(t);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),i.push(a)}return i},$.parseBindingListItem=function(t){return t},$.parseMaybeDefault=function(t,e,r){if(r=r||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(w.eq))return r;var i=this.startNodeAt(t,e);return i.left=r,i.right=this.parseMaybeAssign(),this.finishNode(i,"AssignmentPattern")},$.checkLVal=function(t,e,r){switch(void 0===e&&(e=0),t.type){case"Identifier":2===e&&"let"===t.name&&this.raiseRecoverable(t.start,"let is disallowed as a lexically bound name"),this.strict&&this.reservedWordsStrictBind.test(t.name)&&this.raiseRecoverable(t.start,(e?"Binding ":"Assigning to ")+t.name+" in strict mode"),r&&(O(r,t.name)&&this.raiseRecoverable(t.start,"Argument name clash"),r[t.name]=!0),0!==e&&5!==e&&this.declareName(t.name,e,t.start);break;case"MemberExpression":e&&this.raiseRecoverable(t.start,"Binding member expression");break;case"ObjectPattern":for(var i=0,n=t.properties;i<n.length;i+=1){var s=n[i];this.checkLVal(s,e,r)}break;case"Property":this.checkLVal(t.value,e,r);break;case"ArrayPattern":for(var a=0,o=t.elements;a<o.length;a+=1){var c=o[a];c&&this.checkLVal(c,e,r)}break;case"AssignmentPattern":this.checkLVal(t.left,e,r);break;case"RestElement":this.checkLVal(t.argument,e,r);break;case"ParenthesizedExpression":this.checkLVal(t.expression,e,r);break;default:this.raise(t.start,(e?"Binding":"Assigning to")+" rvalue")}};var Q=D.prototype;Q.checkPropClash=function(t,e,r){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===t.type||this.options.ecmaVersion>=6&&(t.computed||t.method||t.shorthand))){var i,n=t.key;switch(n.type){case"Identifier":i=n.name;break;case"Literal":i=String(n.value);break;default:return}var s=t.kind;if(this.options.ecmaVersion>=6)"__proto__"===i&&"init"===s&&(e.proto&&(r&&r.doubleProto<0?r.doubleProto=n.start:this.raiseRecoverable(n.start,"Redefinition of __proto__ property")),e.proto=!0);else{var a=e[i="$"+i];a?("init"===s?this.strict&&a.init||a.get||a.set:a.init||a[s])&&this.raiseRecoverable(n.start,"Redefinition of property"):a=e[i]={init:!1,get:!1,set:!1},a[s]=!0}}},Q.parseExpression=function(t,e){var r=this.start,i=this.startLoc,n=this.parseMaybeAssign(t,e);if(this.type===w.comma){var s=this.startNodeAt(r,i);for(s.expressions=[n];this.eat(w.comma);)s.expressions.push(this.parseMaybeAssign(t,e));return this.finishNode(s,"SequenceExpression")}return n},Q.parseMaybeAssign=function(t,e,r){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(t);this.exprAllowed=!1}var i=!1,n=-1,s=-1,a=-1;e?(n=e.parenthesizedAssign,s=e.trailingComma,a=e.shorthandAssign,e.parenthesizedAssign=e.trailingComma=e.shorthandAssign=-1):(e=new z,i=!0);var o=this.start,c=this.startLoc;this.type!==w.parenL&&this.type!==w.name||(this.potentialArrowAt=this.start);var h=this.parseMaybeConditional(t,e);if(r&&(h=r.call(this,h,o,c)),this.type.isAssign){var u=this.startNodeAt(o,c);return u.operator=this.value,u.left=this.type===w.eq?this.toAssignable(h,!1,e):h,i||z.call(e),e.shorthandAssign=-1,this.checkLVal(h),this.next(),u.right=this.parseMaybeAssign(t),this.finishNode(u,"AssignmentExpression")}return i&&this.checkExpressionErrors(e,!0),n>-1&&(e.parenthesizedAssign=n),s>-1&&(e.trailingComma=s),a>-1&&(e.shorthandAssign=a),h},Q.parseMaybeConditional=function(t,e){var r=this.start,i=this.startLoc,n=this.parseExprOps(t,e);if(this.checkExpressionErrors(e))return n;if(this.eat(w.question)){var s=this.startNodeAt(r,i);return s.test=n,s.consequent=this.parseMaybeAssign(),this.expect(w.colon),s.alternate=this.parseMaybeAssign(t),this.finishNode(s,"ConditionalExpression")}return n},Q.parseExprOps=function(t,e){var r=this.start,i=this.startLoc,n=this.parseMaybeUnary(e,!1);return this.checkExpressionErrors(e)||n.start===r&&"ArrowFunctionExpression"===n.type?n:this.parseExprOp(n,r,i,-1,t)},Q.parseExprOp=function(t,e,r,i,n){var s=this.type.binop;if(null!=s&&(!n||this.type!==w._in)&&s>i){var a=this.type===w.logicalOR||this.type===w.logicalAND,o=this.value;this.next();var c=this.start,h=this.startLoc,u=this.parseExprOp(this.parseMaybeUnary(null,!1),c,h,s,n),l=this.buildBinary(e,r,t,u,o,a);return this.parseExprOp(l,e,r,i,n)}return t},Q.buildBinary=function(t,e,r,i,n,s){var a=this.startNodeAt(t,e);return a.left=r,a.operator=n,a.right=i,this.finishNode(a,s?"LogicalExpression":"BinaryExpression")},Q.parseMaybeUnary=function(t,e){var r,i=this.start,n=this.startLoc;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction))r=this.parseAwait(),e=!0;else if(this.type.prefix){var s=this.startNode(),a=this.type===w.incDec;s.operator=this.value,s.prefix=!0,this.next(),s.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(t,!0),a?this.checkLVal(s.argument):this.strict&&"delete"===s.operator&&"Identifier"===s.argument.type?this.raiseRecoverable(s.start,"Deleting local variable in strict mode"):e=!0,r=this.finishNode(s,a?"UpdateExpression":"UnaryExpression")}else{if(r=this.parseExprSubscripts(t),this.checkExpressionErrors(t))return r;for(;this.type.postfix&&!this.canInsertSemicolon();){var o=this.startNodeAt(i,n);o.operator=this.value,o.prefix=!1,o.argument=r,this.checkLVal(r),this.next(),r=this.finishNode(o,"UpdateExpression")}}return!e&&this.eat(w.starstar)?this.buildBinary(i,n,r,this.parseMaybeUnary(null,!1),"**",!1):r},Q.parseExprSubscripts=function(t){var e=this.start,r=this.startLoc,i=this.parseExprAtom(t),n="ArrowFunctionExpression"===i.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd);if(this.checkExpressionErrors(t)||n)return i;var s=this.parseSubscripts(i,e,r);return t&&"MemberExpression"===s.type&&(t.parenthesizedAssign>=s.start&&(t.parenthesizedAssign=-1),t.parenthesizedBind>=s.start&&(t.parenthesizedBind=-1)),s},Q.parseSubscripts=function(t,e,r,i){for(var n=this.options.ecmaVersion>=8&&"Identifier"===t.type&&"async"===t.name&&this.lastTokEnd===t.end&&!this.canInsertSemicolon()&&"async"===this.input.slice(t.start,t.end);;){var s=this.parseSubscript(t,e,r,i,n);if(s===t||"ArrowFunctionExpression"===s.type)return s;t=s}},Q.parseSubscript=function(t,e,r,i,n){var s=this.eat(w.bracketL);if(s||this.eat(w.dot)){var a=this.startNodeAt(e,r);a.object=t,a.property=s?this.parseExpression():this.parseIdent("never"!==this.options.allowReserved),a.computed=!!s,s&&this.expect(w.bracketR),t=this.finishNode(a,"MemberExpression")}else if(!i&&this.eat(w.parenL)){var o=new z,c=this.yieldPos,h=this.awaitPos,u=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var l=this.parseExprList(w.parenR,this.options.ecmaVersion>=8,!1,o);if(n&&!this.canInsertSemicolon()&&this.eat(w.arrow))return this.checkPatternErrors(o,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=c,this.awaitPos=h,this.awaitIdentPos=u,this.parseArrowExpression(this.startNodeAt(e,r),l,!0);this.checkExpressionErrors(o,!0),this.yieldPos=c||this.yieldPos,this.awaitPos=h||this.awaitPos,this.awaitIdentPos=u||this.awaitIdentPos;var p=this.startNodeAt(e,r);p.callee=t,p.arguments=l,t=this.finishNode(p,"CallExpression")}else if(this.type===w.backQuote){var f=this.startNodeAt(e,r);f.tag=t,f.quasi=this.parseTemplate({isTagged:!0}),t=this.finishNode(f,"TaggedTemplateExpression")}return t},Q.parseExprAtom=function(t){this.type===w.slash&&this.readRegexp();var e,r=this.potentialArrowAt===this.start;switch(this.type){case w._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),e=this.startNode(),this.next(),this.type!==w.parenL||this.allowDirectSuper||this.raise(e.start,"super() call outside constructor of a subclass"),this.type!==w.dot&&this.type!==w.bracketL&&this.type!==w.parenL&&this.unexpected(),this.finishNode(e,"Super");case w._this:return e=this.startNode(),this.next(),this.finishNode(e,"ThisExpression");case w.name:var i=this.start,n=this.startLoc,s=this.containsEsc,a=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!s&&"async"===a.name&&!this.canInsertSemicolon()&&this.eat(w._function))return this.parseFunction(this.startNodeAt(i,n),0,!1,!0);if(r&&!this.canInsertSemicolon()){if(this.eat(w.arrow))return this.parseArrowExpression(this.startNodeAt(i,n),[a],!1);if(this.options.ecmaVersion>=8&&"async"===a.name&&this.type===w.name&&!s)return a=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(w.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(i,n),[a],!0)}return a;case w.regexp:var o=this.value;return(e=this.parseLiteral(o.value)).regex={pattern:o.pattern,flags:o.flags},e;case w.num:case w.string:return this.parseLiteral(this.value);case w._null:case w._true:case w._false:return(e=this.startNode()).value=this.type===w._null?null:this.type===w._true,e.raw=this.type.keyword,this.next(),this.finishNode(e,"Literal");case w.parenL:var c=this.start,h=this.parseParenAndDistinguishExpression(r);return t&&(t.parenthesizedAssign<0&&!this.isSimpleAssignTarget(h)&&(t.parenthesizedAssign=c),t.parenthesizedBind<0&&(t.parenthesizedBind=c)),h;case w.bracketL:return e=this.startNode(),this.next(),e.elements=this.parseExprList(w.bracketR,!0,!0,t),this.finishNode(e,"ArrayExpression");case w.braceL:return this.parseObj(!1,t);case w._function:return e=this.startNode(),this.next(),this.parseFunction(e,0);case w._class:return this.parseClass(this.startNode(),!1);case w._new:return this.parseNew();case w.backQuote:return this.parseTemplate();case w._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},Q.parseExprImport=function(){var t=this.startNode();if(this.next(),this.type===w.parenL)return this.parseDynamicImport(t);this.unexpected()},Q.parseDynamicImport=function(t){if(this.next(),t.source=this.parseMaybeAssign(),!this.eat(w.parenR)){var e=this.start;this.eat(w.comma)&&this.eat(w.parenR)?this.raiseRecoverable(e,"Trailing comma is not allowed in import()"):this.unexpected(e)}return this.finishNode(t,"ImportExpression")},Q.parseLiteral=function(t){var e=this.startNode();return e.value=t,e.raw=this.input.slice(this.start,this.end),110===e.raw.charCodeAt(e.raw.length-1)&&(e.bigint=e.raw.slice(0,-1)),this.next(),this.finishNode(e,"Literal")},Q.parseParenExpression=function(){this.expect(w.parenL);var t=this.parseExpression();return this.expect(w.parenR),t},Q.parseParenAndDistinguishExpression=function(t){var e,r=this.start,i=this.startLoc,n=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var s,a=this.start,o=this.startLoc,c=[],h=!0,u=!1,l=new z,p=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==w.parenR;){if(h?h=!1:this.expect(w.comma),n&&this.afterTrailingComma(w.parenR,!0)){u=!0;break}if(this.type===w.ellipsis){s=this.start,c.push(this.parseParenItem(this.parseRestBinding())),this.type===w.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}c.push(this.parseMaybeAssign(!1,l,this.parseParenItem))}var d=this.start,m=this.startLoc;if(this.expect(w.parenR),t&&!this.canInsertSemicolon()&&this.eat(w.arrow))return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=p,this.awaitPos=f,this.parseParenArrowList(r,i,c);c.length&&!u||this.unexpected(this.lastTokStart),s&&this.unexpected(s),this.checkExpressionErrors(l,!0),this.yieldPos=p||this.yieldPos,this.awaitPos=f||this.awaitPos,c.length>1?((e=this.startNodeAt(a,o)).expressions=c,this.finishNodeAt(e,"SequenceExpression",d,m)):e=c[0]}else e=this.parseParenExpression();if(this.options.preserveParens){var g=this.startNodeAt(r,i);return g.expression=e,this.finishNode(g,"ParenthesizedExpression")}return e},Q.parseParenItem=function(t){return t},Q.parseParenArrowList=function(t,e,r){return this.parseArrowExpression(this.startNodeAt(t,e),r)};var Z=[];Q.parseNew=function(){var t=this.startNode(),e=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(w.dot)){t.meta=e;var r=this.containsEsc;return t.property=this.parseIdent(!0),("target"!==t.property.name||r)&&this.raiseRecoverable(t.property.start,"The only valid meta property for new is new.target"),this.inNonArrowFunction()||this.raiseRecoverable(t.start,"new.target can only be used in functions"),this.finishNode(t,"MetaProperty")}var i=this.start,n=this.startLoc,s=this.type===w._import;return t.callee=this.parseSubscripts(this.parseExprAtom(),i,n,!0),s&&"ImportExpression"===t.callee.type&&this.raise(i,"Cannot use new with import()"),this.eat(w.parenL)?t.arguments=this.parseExprList(w.parenR,this.options.ecmaVersion>=8,!1):t.arguments=Z,this.finishNode(t,"NewExpression")},Q.parseTemplateElement=function(t){var e=t.isTagged,r=this.startNode();return this.type===w.invalidTemplate?(e||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),r.value={raw:this.value,cooked:null}):r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),r.tail=this.type===w.backQuote,this.finishNode(r,"TemplateElement")},Q.parseTemplate=function(t){void 0===t&&(t={});var e=t.isTagged;void 0===e&&(e=!1);var r=this.startNode();this.next(),r.expressions=[];var i=this.parseTemplateElement({isTagged:e});for(r.quasis=[i];!i.tail;)this.type===w.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(w.dollarBraceL),r.expressions.push(this.parseExpression()),this.expect(w.braceR),r.quasis.push(i=this.parseTemplateElement({isTagged:e}));return this.next(),this.finishNode(r,"TemplateLiteral")},Q.isAsyncProp=function(t){return!t.computed&&"Identifier"===t.key.type&&"async"===t.key.name&&(this.type===w.name||this.type===w.num||this.type===w.string||this.type===w.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===w.star)&&!_.test(this.input.slice(this.lastTokEnd,this.start))},Q.parseObj=function(t,e){var r=this.startNode(),i=!0,n={};for(r.properties=[],this.next();!this.eat(w.braceR);){if(i)i=!1;else if(this.expect(w.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(w.braceR))break;var s=this.parseProperty(t,e);t||this.checkPropClash(s,n,e),r.properties.push(s)}return this.finishNode(r,t?"ObjectPattern":"ObjectExpression")},Q.parseProperty=function(t,e){var r,i,n,s,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(w.ellipsis))return t?(a.argument=this.parseIdent(!1),this.type===w.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(this.type===w.parenL&&e&&(e.parenthesizedAssign<0&&(e.parenthesizedAssign=this.start),e.parenthesizedBind<0&&(e.parenthesizedBind=this.start)),a.argument=this.parseMaybeAssign(!1,e),this.type===w.comma&&e&&e.trailingComma<0&&(e.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(t||e)&&(n=this.start,s=this.startLoc),t||(r=this.eat(w.star)));var o=this.containsEsc;return this.parsePropertyName(a),!t&&!o&&this.options.ecmaVersion>=8&&!r&&this.isAsyncProp(a)?(i=!0,r=this.options.ecmaVersion>=9&&this.eat(w.star),this.parsePropertyName(a,e)):i=!1,this.parsePropertyValue(a,t,r,i,n,s,e,o),this.finishNode(a,"Property")},Q.parsePropertyValue=function(t,e,r,i,n,s,a,o){if((r||i)&&this.type===w.colon&&this.unexpected(),this.eat(w.colon))t.value=e?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),t.kind="init";else if(this.options.ecmaVersion>=6&&this.type===w.parenL)e&&this.unexpected(),t.kind="init",t.method=!0,t.value=this.parseMethod(r,i);else if(e||o||!(this.options.ecmaVersion>=5)||t.computed||"Identifier"!==t.key.type||"get"!==t.key.name&&"set"!==t.key.name||this.type===w.comma||this.type===w.braceR)this.options.ecmaVersion>=6&&!t.computed&&"Identifier"===t.key.type?((r||i)&&this.unexpected(),this.checkUnreserved(t.key),"await"!==t.key.name||this.awaitIdentPos||(this.awaitIdentPos=n),t.kind="init",e?t.value=this.parseMaybeDefault(n,s,t.key):this.type===w.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),t.value=this.parseMaybeDefault(n,s,t.key)):t.value=t.key,t.shorthand=!0):this.unexpected();else{(r||i)&&this.unexpected(),t.kind=t.key.name,this.parsePropertyName(t),t.value=this.parseMethod(!1);var c="get"===t.kind?0:1;if(t.value.params.length!==c){var h=t.value.start;"get"===t.kind?this.raiseRecoverable(h,"getter should have no params"):this.raiseRecoverable(h,"setter should have exactly one param")}else"set"===t.kind&&"RestElement"===t.value.params[0].type&&this.raiseRecoverable(t.value.params[0].start,"Setter cannot use rest params")}},Q.parsePropertyName=function(t){if(this.options.ecmaVersion>=6){if(this.eat(w.bracketL))return t.computed=!0,t.key=this.parseMaybeAssign(),this.expect(w.bracketR),t.key;t.computed=!1}return t.key=this.type===w.num||this.type===w.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},Q.initFunction=function(t){t.id=null,this.options.ecmaVersion>=6&&(t.generator=t.expression=!1),this.options.ecmaVersion>=8&&(t.async=!1)},Q.parseMethod=function(t,e,r){var i=this.startNode(),n=this.yieldPos,s=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(i),this.options.ecmaVersion>=6&&(i.generator=t),this.options.ecmaVersion>=8&&(i.async=!!e),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|M(e,i.generator)|(r?128:0)),this.expect(w.parenL),i.params=this.parseBindingList(w.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(i,!1,!0),this.yieldPos=n,this.awaitPos=s,this.awaitIdentPos=a,this.finishNode(i,"FunctionExpression")},Q.parseArrowExpression=function(t,e,r){var i=this.yieldPos,n=this.awaitPos,s=this.awaitIdentPos;return this.enterScope(16|M(r,!1)),this.initFunction(t),this.options.ecmaVersion>=8&&(t.async=!!r),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,t.params=this.toAssignableList(e,!0),this.parseFunctionBody(t,!0,!1),this.yieldPos=i,this.awaitPos=n,this.awaitIdentPos=s,this.finishNode(t,"ArrowFunctionExpression")},Q.parseFunctionBody=function(t,e,r){var i=e&&this.type!==w.braceL,n=this.strict,s=!1;if(i)t.body=this.parseMaybeAssign(),t.expression=!0,this.checkParams(t,!1);else{var a=this.options.ecmaVersion>=7&&!this.isSimpleParamList(t.params);n&&!a||(s=this.strictDirective(this.end))&&a&&this.raiseRecoverable(t.start,"Illegal 'use strict' directive in function with non-simple parameter list");var o=this.labels;this.labels=[],s&&(this.strict=!0),this.checkParams(t,!n&&!s&&!e&&!r&&this.isSimpleParamList(t.params)),t.body=this.parseBlock(!1),t.expression=!1,this.adaptDirectivePrologue(t.body.body),this.labels=o}this.exitScope(),this.strict&&t.id&&this.checkLVal(t.id,5),this.strict=n},Q.isSimpleParamList=function(t){for(var e=0,r=t;e<r.length;e+=1)if("Identifier"!==r[e].type)return!1;return!0},Q.checkParams=function(t,e){for(var r={},i=0,n=t.params;i<n.length;i+=1){var s=n[i];this.checkLVal(s,1,e?null:r)}},Q.parseExprList=function(t,e,r,i){for(var n=[],s=!0;!this.eat(t);){if(s)s=!1;else if(this.expect(w.comma),e&&this.afterTrailingComma(t))break;var a=void 0;r&&this.type===w.comma?a=null:this.type===w.ellipsis?(a=this.parseSpread(i),i&&this.type===w.comma&&i.trailingComma<0&&(i.trailingComma=this.start)):a=this.parseMaybeAssign(!1,i),n.push(a)}return n},Q.checkUnreserved=function(t){var e=t.start,r=t.end,i=t.name;this.inGenerator&&"yield"===i&&this.raiseRecoverable(e,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===i&&this.raiseRecoverable(e,"Cannot use 'await' as identifier inside an async function"),this.keywords.test(i)&&this.raise(e,"Unexpected keyword '"+i+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(e,r).indexOf("\\")||(this.strict?this.reservedWordsStrict:this.reservedWords).test(i)&&(this.inAsync||"await"!==i||this.raiseRecoverable(e,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(e,"The keyword '"+i+"' is reserved"))},Q.parseIdent=function(t,e){var r=this.startNode();return this.type===w.name?r.name=this.value:this.type.keyword?(r.name=this.type.keyword,"class"!==r.name&&"function"!==r.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(),this.finishNode(r,"Identifier"),t||(this.checkUnreserved(r),"await"!==r.name||this.awaitIdentPos||(this.awaitIdentPos=r.start)),r},Q.parseYield=function(t){this.yieldPos||(this.yieldPos=this.start);var e=this.startNode();return this.next(),this.type===w.semi||this.canInsertSemicolon()||this.type!==w.star&&!this.type.startsExpr?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(w.star),e.argument=this.parseMaybeAssign(t)),this.finishNode(e,"YieldExpression")},Q.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0),this.finishNode(t,"AwaitExpression")};var Y=D.prototype;Y.raise=function(t,e){var r=R(this.input,t);e+=" ("+r.line+":"+r.column+")";var i=new SyntaxError(e);throw i.pos=t,i.loc=r,i.raisedAt=this.pos,i},Y.raiseRecoverable=Y.raise,Y.curPosition=function(){if(this.options.locations)return new j(this.curLine,this.pos-this.lineStart)};var tt=D.prototype,et=function(t){this.flags=t,this.var=[],this.lexical=[],this.functions=[]};tt.enterScope=function(t){this.scopeStack.push(new et(t))},tt.exitScope=function(){this.scopeStack.pop()},tt.treatFunctionsAsVarInScope=function(t){return 2&t.flags||!this.inModule&&1&t.flags},tt.declareName=function(t,e,r){var i=!1;if(2===e){var n=this.currentScope();i=n.lexical.indexOf(t)>-1||n.functions.indexOf(t)>-1||n.var.indexOf(t)>-1,n.lexical.push(t),this.inModule&&1&n.flags&&delete this.undefinedExports[t]}else if(4===e)this.currentScope().lexical.push(t);else if(3===e){var s=this.currentScope();i=this.treatFunctionsAsVar?s.lexical.indexOf(t)>-1:s.lexical.indexOf(t)>-1||s.var.indexOf(t)>-1,s.functions.push(t)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(t)>-1&&!(32&o.flags&&o.lexical[0]===t)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(t)>-1){i=!0;break}if(o.var.push(t),this.inModule&&1&o.flags&&delete this.undefinedExports[t],3&o.flags)break}i&&this.raiseRecoverable(r,"Identifier '"+t+"' has already been declared")},tt.checkLocalExport=function(t){-1===this.scopeStack[0].lexical.indexOf(t.name)&&-1===this.scopeStack[0].var.indexOf(t.name)&&(this.undefinedExports[t.name]=t)},tt.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},tt.currentVarScope=function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if(3&e.flags)return e}},tt.currentThisScope=function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if(3&e.flags&&!(16&e.flags))return e}};var rt=function(t,e,r){this.type="",this.start=e,this.end=0,t.options.locations&&(this.loc=new V(t,r)),t.options.directSourceFile&&(this.sourceFile=t.options.directSourceFile),t.options.ranges&&(this.range=[e,0])},it=D.prototype;function nt(t,e,r,i){return t.type=e,t.end=r,this.options.locations&&(t.loc.end=i),this.options.ranges&&(t.range[1]=r),t}it.startNode=function(){return new rt(this,this.start,this.startLoc)},it.startNodeAt=function(t,e){return new rt(this,t,e)},it.finishNode=function(t,e){return nt.call(this,t,e,this.lastTokEnd,this.lastTokEndLoc)},it.finishNodeAt=function(t,e,r,i){return nt.call(this,t,e,r,i)};var st=function(t,e,r,i,n){this.token=t,this.isExpr=!!e,this.preserveSpace=!!r,this.override=i,this.generator=!!n},at={b_stat:new st("{",!1),b_expr:new st("{",!0),b_tmpl:new st("${",!1),p_stat:new st("(",!1),p_expr:new st("(",!0),q_tmpl:new st("`",!0,!0,(function(t){return t.tryReadTemplateToken()})),f_stat:new st("function",!1),f_expr:new st("function",!0),f_expr_gen:new st("function",!0,!1,null,!0),f_gen:new st("function",!1,!1,null,!0)},ot=D.prototype;ot.initialContext=function(){return[at.b_stat]},ot.braceIsBlock=function(t){var e=this.curContext();return e===at.f_expr||e===at.f_stat||(t!==w.colon||e!==at.b_stat&&e!==at.b_expr?t===w._return||t===w.name&&this.exprAllowed?_.test(this.input.slice(this.lastTokEnd,this.start)):t===w._else||t===w.semi||t===w.eof||t===w.parenR||t===w.arrow||(t===w.braceL?e===at.b_stat:t!==w._var&&t!==w._const&&t!==w.name&&!this.exprAllowed):!e.isExpr)},ot.inGeneratorContext=function(){for(var t=this.context.length-1;t>=1;t--){var e=this.context[t];if("function"===e.token)return e.generator}return!1},ot.updateContext=function(t){var e,r=this.type;r.keyword&&t===w.dot?this.exprAllowed=!1:(e=r.updateContext)?e.call(this,t):this.exprAllowed=r.beforeExpr},w.parenR.updateContext=w.braceR.updateContext=function(){if(1!==this.context.length){var t=this.context.pop();t===at.b_stat&&"function"===this.curContext().token&&(t=this.context.pop()),this.exprAllowed=!t.isExpr}else this.exprAllowed=!0},w.braceL.updateContext=function(t){this.context.push(this.braceIsBlock(t)?at.b_stat:at.b_expr),this.exprAllowed=!0},w.dollarBraceL.updateContext=function(){this.context.push(at.b_tmpl),this.exprAllowed=!0},w.parenL.updateContext=function(t){var e=t===w._if||t===w._for||t===w._with||t===w._while;this.context.push(e?at.p_stat:at.p_expr),this.exprAllowed=!0},w.incDec.updateContext=function(){},w._function.updateContext=w._class.updateContext=function(t){!t.beforeExpr||t===w.semi||t===w._else||t===w._return&&_.test(this.input.slice(this.lastTokEnd,this.start))||(t===w.colon||t===w.braceL)&&this.curContext()===at.b_stat?this.context.push(at.f_stat):this.context.push(at.f_expr),this.exprAllowed=!1},w.backQuote.updateContext=function(){this.curContext()===at.q_tmpl?this.context.pop():this.context.push(at.q_tmpl),this.exprAllowed=!1},w.star.updateContext=function(t){if(t===w._function){var e=this.context.length-1;this.context[e]===at.f_expr?this.context[e]=at.f_expr_gen:this.context[e]=at.f_gen}this.exprAllowed=!0},w.name.updateContext=function(t){var e=!1;this.options.ecmaVersion>=6&&t!==w.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(e=!0),this.exprAllowed=e};var ct="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",ht={9:ct,10:ct+" Extended_Pictographic",11:"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS Extended_Pictographic"},ut="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",lt="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",pt={9:lt,10:lt+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",11:"Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho"},ft={};function dt(t){var e=ft[t]={binary:L(ht[t]+" "+ut),nonBinary:{General_Category:L(ut),Script:L(pt[t])}};e.nonBinary.Script_Extensions=e.nonBinary.Script,e.nonBinary.gc=e.nonBinary.General_Category,e.nonBinary.sc=e.nonBinary.Script,e.nonBinary.scx=e.nonBinary.Script_Extensions}dt(9),dt(10),dt(11);var mt=D.prototype,gt=function(t){this.parser=t,this.validFlags="gim"+(t.options.ecmaVersion>=6?"uy":"")+(t.options.ecmaVersion>=9?"s":""),this.unicodeProperties=ft[t.options.ecmaVersion>=11?11:t.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function vt(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function xt(t){return 36===t||t>=40&&t<=43||46===t||63===t||t>=91&&t<=94||t>=123&&t<=125}function yt(t){return t>=65&&t<=90||t>=97&&t<=122}function bt(t){return yt(t)||95===t}function kt(t){return bt(t)||wt(t)}function wt(t){return t>=48&&t<=57}function _t(t){return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function At(t){return t>=65&&t<=70?t-65+10:t>=97&&t<=102?t-97+10:t-48}function Ct(t){return t>=48&&t<=55}gt.prototype.reset=function(t,e,r){var i=-1!==r.indexOf("u");this.start=0|t,this.source=e+"",this.flags=r,this.switchU=i&&this.parser.options.ecmaVersion>=6,this.switchN=i&&this.parser.options.ecmaVersion>=9},gt.prototype.raise=function(t){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+t)},gt.prototype.at=function(t){var e=this.source,r=e.length;if(t>=r)return-1;var i=e.charCodeAt(t);return!this.switchU||i<=55295||i>=57344||t+1>=r?i:(i<<10)+e.charCodeAt(t+1)-56613888},gt.prototype.nextIndex=function(t){var e=this.source,r=e.length;if(t>=r)return r;var i=e.charCodeAt(t);return!this.switchU||i<=55295||i>=57344||t+1>=r?t+1:t+2},gt.prototype.current=function(){return this.at(this.pos)},gt.prototype.lookahead=function(){return this.at(this.nextIndex(this.pos))},gt.prototype.advance=function(){this.pos=this.nextIndex(this.pos)},gt.prototype.eat=function(t){return this.current()===t&&(this.advance(),!0)},mt.validateRegExpFlags=function(t){for(var e=t.validFlags,r=t.flags,i=0;i<r.length;i++){var n=r.charAt(i);-1===e.indexOf(n)&&this.raise(t.start,"Invalid regular expression flag"),r.indexOf(n,i+1)>-1&&this.raise(t.start,"Duplicate regular expression flag")}},mt.validateRegExpPattern=function(t){this.regexp_pattern(t),!t.switchN&&this.options.ecmaVersion>=9&&t.groupNames.length>0&&(t.switchN=!0,this.regexp_pattern(t))},mt.regexp_pattern=function(t){t.pos=0,t.lastIntValue=0,t.lastStringValue="",t.lastAssertionIsQuantifiable=!1,t.numCapturingParens=0,t.maxBackReference=0,t.groupNames.length=0,t.backReferenceNames.length=0,this.regexp_disjunction(t),t.pos!==t.source.length&&(t.eat(41)&&t.raise("Unmatched ')'"),(t.eat(93)||t.eat(125))&&t.raise("Lone quantifier brackets")),t.maxBackReference>t.numCapturingParens&&t.raise("Invalid escape");for(var e=0,r=t.backReferenceNames;e<r.length;e+=1){var i=r[e];-1===t.groupNames.indexOf(i)&&t.raise("Invalid named capture referenced")}},mt.regexp_disjunction=function(t){for(this.regexp_alternative(t);t.eat(124);)this.regexp_alternative(t);this.regexp_eatQuantifier(t,!0)&&t.raise("Nothing to repeat"),t.eat(123)&&t.raise("Lone quantifier brackets")},mt.regexp_alternative=function(t){for(;t.pos<t.source.length&&this.regexp_eatTerm(t););},mt.regexp_eatTerm=function(t){return this.regexp_eatAssertion(t)?(t.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(t)&&t.switchU&&t.raise("Invalid quantifier"),!0):!(t.switchU?!this.regexp_eatAtom(t):!this.regexp_eatExtendedAtom(t))&&(this.regexp_eatQuantifier(t),!0)},mt.regexp_eatAssertion=function(t){var e=t.pos;if(t.lastAssertionIsQuantifiable=!1,t.eat(94)||t.eat(36))return!0;if(t.eat(92)){if(t.eat(66)||t.eat(98))return!0;t.pos=e}if(t.eat(40)&&t.eat(63)){var r=!1;if(this.options.ecmaVersion>=9&&(r=t.eat(60)),t.eat(61)||t.eat(33))return this.regexp_disjunction(t),t.eat(41)||t.raise("Unterminated group"),t.lastAssertionIsQuantifiable=!r,!0}return t.pos=e,!1},mt.regexp_eatQuantifier=function(t,e){return void 0===e&&(e=!1),!!this.regexp_eatQuantifierPrefix(t,e)&&(t.eat(63),!0)},mt.regexp_eatQuantifierPrefix=function(t,e){return t.eat(42)||t.eat(43)||t.eat(63)||this.regexp_eatBracedQuantifier(t,e)},mt.regexp_eatBracedQuantifier=function(t,e){var r=t.pos;if(t.eat(123)){var i=0,n=-1;if(this.regexp_eatDecimalDigits(t)&&(i=t.lastIntValue,t.eat(44)&&this.regexp_eatDecimalDigits(t)&&(n=t.lastIntValue),t.eat(125)))return-1!==n&&n<i&&!e&&t.raise("numbers out of order in {} quantifier"),!0;t.switchU&&!e&&t.raise("Incomplete quantifier"),t.pos=r}return!1},mt.regexp_eatAtom=function(t){return this.regexp_eatPatternCharacters(t)||t.eat(46)||this.regexp_eatReverseSolidusAtomEscape(t)||this.regexp_eatCharacterClass(t)||this.regexp_eatUncapturingGroup(t)||this.regexp_eatCapturingGroup(t)},mt.regexp_eatReverseSolidusAtomEscape=function(t){var e=t.pos;if(t.eat(92)){if(this.regexp_eatAtomEscape(t))return!0;t.pos=e}return!1},mt.regexp_eatUncapturingGroup=function(t){var e=t.pos;if(t.eat(40)){if(t.eat(63)&&t.eat(58)){if(this.regexp_disjunction(t),t.eat(41))return!0;t.raise("Unterminated group")}t.pos=e}return!1},mt.regexp_eatCapturingGroup=function(t){if(t.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(t):63===t.current()&&t.raise("Invalid group"),this.regexp_disjunction(t),t.eat(41))return t.numCapturingParens+=1,!0;t.raise("Unterminated group")}return!1},mt.regexp_eatExtendedAtom=function(t){return t.eat(46)||this.regexp_eatReverseSolidusAtomEscape(t)||this.regexp_eatCharacterClass(t)||this.regexp_eatUncapturingGroup(t)||this.regexp_eatCapturingGroup(t)||this.regexp_eatInvalidBracedQuantifier(t)||this.regexp_eatExtendedPatternCharacter(t)},mt.regexp_eatInvalidBracedQuantifier=function(t){return this.regexp_eatBracedQuantifier(t,!0)&&t.raise("Nothing to repeat"),!1},mt.regexp_eatSyntaxCharacter=function(t){var e=t.current();return!!xt(e)&&(t.lastIntValue=e,t.advance(),!0)},mt.regexp_eatPatternCharacters=function(t){for(var e=t.pos,r=0;-1!==(r=t.current())&&!xt(r);)t.advance();return t.pos!==e},mt.regexp_eatExtendedPatternCharacter=function(t){var e=t.current();return!(-1===e||36===e||e>=40&&e<=43||46===e||63===e||91===e||94===e||124===e||(t.advance(),0))},mt.regexp_groupSpecifier=function(t){if(t.eat(63)){if(this.regexp_eatGroupName(t))return-1!==t.groupNames.indexOf(t.lastStringValue)&&t.raise("Duplicate capture group name"),void t.groupNames.push(t.lastStringValue);t.raise("Invalid group")}},mt.regexp_eatGroupName=function(t){if(t.lastStringValue="",t.eat(60)){if(this.regexp_eatRegExpIdentifierName(t)&&t.eat(62))return!0;t.raise("Invalid capture group name")}return!1},mt.regexp_eatRegExpIdentifierName=function(t){if(t.lastStringValue="",this.regexp_eatRegExpIdentifierStart(t)){for(t.lastStringValue+=vt(t.lastIntValue);this.regexp_eatRegExpIdentifierPart(t);)t.lastStringValue+=vt(t.lastIntValue);return!0}return!1},mt.regexp_eatRegExpIdentifierStart=function(t){var e=t.pos,r=t.current();return t.advance(),92===r&&this.regexp_eatRegExpUnicodeEscapeSequence(t)&&(r=t.lastIntValue),function(t){return d(t,!0)||36===t||95===t}(r)?(t.lastIntValue=r,!0):(t.pos=e,!1)},mt.regexp_eatRegExpIdentifierPart=function(t){var e=t.pos,r=t.current();return t.advance(),92===r&&this.regexp_eatRegExpUnicodeEscapeSequence(t)&&(r=t.lastIntValue),function(t){return m(t,!0)||36===t||95===t||8204===t||8205===t}(r)?(t.lastIntValue=r,!0):(t.pos=e,!1)},mt.regexp_eatAtomEscape=function(t){return!!(this.regexp_eatBackReference(t)||this.regexp_eatCharacterClassEscape(t)||this.regexp_eatCharacterEscape(t)||t.switchN&&this.regexp_eatKGroupName(t))||(t.switchU&&(99===t.current()&&t.raise("Invalid unicode escape"),t.raise("Invalid escape")),!1)},mt.regexp_eatBackReference=function(t){var e=t.pos;if(this.regexp_eatDecimalEscape(t)){var r=t.lastIntValue;if(t.switchU)return r>t.maxBackReference&&(t.maxBackReference=r),!0;if(r<=t.numCapturingParens)return!0;t.pos=e}return!1},mt.regexp_eatKGroupName=function(t){if(t.eat(107)){if(this.regexp_eatGroupName(t))return t.backReferenceNames.push(t.lastStringValue),!0;t.raise("Invalid named reference")}return!1},mt.regexp_eatCharacterEscape=function(t){return this.regexp_eatControlEscape(t)||this.regexp_eatCControlLetter(t)||this.regexp_eatZero(t)||this.regexp_eatHexEscapeSequence(t)||this.regexp_eatRegExpUnicodeEscapeSequence(t)||!t.switchU&&this.regexp_eatLegacyOctalEscapeSequence(t)||this.regexp_eatIdentityEscape(t)},mt.regexp_eatCControlLetter=function(t){var e=t.pos;if(t.eat(99)){if(this.regexp_eatControlLetter(t))return!0;t.pos=e}return!1},mt.regexp_eatZero=function(t){return 48===t.current()&&!wt(t.lookahead())&&(t.lastIntValue=0,t.advance(),!0)},mt.regexp_eatControlEscape=function(t){var e=t.current();return 116===e?(t.lastIntValue=9,t.advance(),!0):110===e?(t.lastIntValue=10,t.advance(),!0):118===e?(t.lastIntValue=11,t.advance(),!0):102===e?(t.lastIntValue=12,t.advance(),!0):114===e&&(t.lastIntValue=13,t.advance(),!0)},mt.regexp_eatControlLetter=function(t){var e=t.current();return!!yt(e)&&(t.lastIntValue=e%32,t.advance(),!0)},mt.regexp_eatRegExpUnicodeEscapeSequence=function(t){var e,r=t.pos;if(t.eat(117)){if(this.regexp_eatFixedHexDigits(t,4)){var i=t.lastIntValue;if(t.switchU&&i>=55296&&i<=56319){var n=t.pos;if(t.eat(92)&&t.eat(117)&&this.regexp_eatFixedHexDigits(t,4)){var s=t.lastIntValue;if(s>=56320&&s<=57343)return t.lastIntValue=1024*(i-55296)+(s-56320)+65536,!0}t.pos=n,t.lastIntValue=i}return!0}if(t.switchU&&t.eat(123)&&this.regexp_eatHexDigits(t)&&t.eat(125)&&(e=t.lastIntValue)>=0&&e<=1114111)return!0;t.switchU&&t.raise("Invalid unicode escape"),t.pos=r}return!1},mt.regexp_eatIdentityEscape=function(t){if(t.switchU)return!!this.regexp_eatSyntaxCharacter(t)||!!t.eat(47)&&(t.lastIntValue=47,!0);var e=t.current();return!(99===e||t.switchN&&107===e||(t.lastIntValue=e,t.advance(),0))},mt.regexp_eatDecimalEscape=function(t){t.lastIntValue=0;var e=t.current();if(e>=49&&e<=57){do{t.lastIntValue=10*t.lastIntValue+(e-48),t.advance()}while((e=t.current())>=48&&e<=57);return!0}return!1},mt.regexp_eatCharacterClassEscape=function(t){var e=t.current();if(function(t){return 100===t||68===t||115===t||83===t||119===t||87===t}(e))return t.lastIntValue=-1,t.advance(),!0;if(t.switchU&&this.options.ecmaVersion>=9&&(80===e||112===e)){if(t.lastIntValue=-1,t.advance(),t.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(t)&&t.eat(125))return!0;t.raise("Invalid property name")}return!1},mt.regexp_eatUnicodePropertyValueExpression=function(t){var e=t.pos;if(this.regexp_eatUnicodePropertyName(t)&&t.eat(61)){var r=t.lastStringValue;if(this.regexp_eatUnicodePropertyValue(t)){var i=t.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(t,r,i),!0}}if(t.pos=e,this.regexp_eatLoneUnicodePropertyNameOrValue(t)){var n=t.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(t,n),!0}return!1},mt.regexp_validateUnicodePropertyNameAndValue=function(t,e,r){O(t.unicodeProperties.nonBinary,e)||t.raise("Invalid property name"),t.unicodeProperties.nonBinary[e].test(r)||t.raise("Invalid property value")},mt.regexp_validateUnicodePropertyNameOrValue=function(t,e){t.unicodeProperties.binary.test(e)||t.raise("Invalid property name")},mt.regexp_eatUnicodePropertyName=function(t){var e=0;for(t.lastStringValue="";bt(e=t.current());)t.lastStringValue+=vt(e),t.advance();return""!==t.lastStringValue},mt.regexp_eatUnicodePropertyValue=function(t){var e=0;for(t.lastStringValue="";kt(e=t.current());)t.lastStringValue+=vt(e),t.advance();return""!==t.lastStringValue},mt.regexp_eatLoneUnicodePropertyNameOrValue=function(t){return this.regexp_eatUnicodePropertyValue(t)},mt.regexp_eatCharacterClass=function(t){if(t.eat(91)){if(t.eat(94),this.regexp_classRanges(t),t.eat(93))return!0;t.raise("Unterminated character class")}return!1},mt.regexp_classRanges=function(t){for(;this.regexp_eatClassAtom(t);){var e=t.lastIntValue;if(t.eat(45)&&this.regexp_eatClassAtom(t)){var r=t.lastIntValue;!t.switchU||-1!==e&&-1!==r||t.raise("Invalid character class"),-1!==e&&-1!==r&&e>r&&t.raise("Range out of order in character class")}}},mt.regexp_eatClassAtom=function(t){var e=t.pos;if(t.eat(92)){if(this.regexp_eatClassEscape(t))return!0;if(t.switchU){var r=t.current();(99===r||Ct(r))&&t.raise("Invalid class escape"),t.raise("Invalid escape")}t.pos=e}var i=t.current();return 93!==i&&(t.lastIntValue=i,t.advance(),!0)},mt.regexp_eatClassEscape=function(t){var e=t.pos;if(t.eat(98))return t.lastIntValue=8,!0;if(t.switchU&&t.eat(45))return t.lastIntValue=45,!0;if(!t.switchU&&t.eat(99)){if(this.regexp_eatClassControlLetter(t))return!0;t.pos=e}return this.regexp_eatCharacterClassEscape(t)||this.regexp_eatCharacterEscape(t)},mt.regexp_eatClassControlLetter=function(t){var e=t.current();return!(!wt(e)&&95!==e||(t.lastIntValue=e%32,t.advance(),0))},mt.regexp_eatHexEscapeSequence=function(t){var e=t.pos;if(t.eat(120)){if(this.regexp_eatFixedHexDigits(t,2))return!0;t.switchU&&t.raise("Invalid escape"),t.pos=e}return!1},mt.regexp_eatDecimalDigits=function(t){var e=t.pos,r=0;for(t.lastIntValue=0;wt(r=t.current());)t.lastIntValue=10*t.lastIntValue+(r-48),t.advance();return t.pos!==e},mt.regexp_eatHexDigits=function(t){var e=t.pos,r=0;for(t.lastIntValue=0;_t(r=t.current());)t.lastIntValue=16*t.lastIntValue+At(r),t.advance();return t.pos!==e},mt.regexp_eatLegacyOctalEscapeSequence=function(t){if(this.regexp_eatOctalDigit(t)){var e=t.lastIntValue;if(this.regexp_eatOctalDigit(t)){var r=t.lastIntValue;e<=3&&this.regexp_eatOctalDigit(t)?t.lastIntValue=64*e+8*r+t.lastIntValue:t.lastIntValue=8*e+r}else t.lastIntValue=e;return!0}return!1},mt.regexp_eatOctalDigit=function(t){var e=t.current();return Ct(e)?(t.lastIntValue=e-48,t.advance(),!0):(t.lastIntValue=0,!1)},mt.regexp_eatFixedHexDigits=function(t,e){var r=t.pos;t.lastIntValue=0;for(var i=0;i<e;++i){var n=t.current();if(!_t(n))return t.pos=r,!1;t.lastIntValue=16*t.lastIntValue+At(n),t.advance()}return!0};var Et=function(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,t.options.locations&&(this.loc=new V(t,t.startLoc,t.endLoc)),t.options.ranges&&(this.range=[t.start,t.end])},St=D.prototype;function Pt(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}St.next=function(){this.options.onToken&&this.options.onToken(new Et(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},St.getToken=function(){return this.next(),new Et(this)},"undefined"!=typeof Symbol&&(St[Symbol.iterator]=function(){var t=this;return{next:function(){var e=t.getToken();return{done:e.type===w.eof,value:e}}}}),St.curContext=function(){return this.context[this.context.length-1]},St.nextToken=function(){var t=this.curContext();return t&&t.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(w.eof):t.override?t.override(this):void this.readToken(this.fullCharCodeAtPos())},St.readToken=function(t){return d(t,this.options.ecmaVersion>=6)||92===t?this.readWord():this.getTokenFromCode(t)},St.fullCharCodeAtPos=function(){var t=this.input.charCodeAt(this.pos);return t<=55295||t>=57344?t:(t<<10)+this.input.charCodeAt(this.pos+1)-56613888},St.skipBlockComment=function(){var t,e=this.options.onComment&&this.curPosition(),r=this.pos,i=this.input.indexOf("*/",this.pos+=2);if(-1===i&&this.raise(this.pos-2,"Unterminated comment"),this.pos=i+2,this.options.locations)for(A.lastIndex=r;(t=A.exec(this.input))&&t.index<this.pos;)++this.curLine,this.lineStart=t.index+t[0].length;this.options.onComment&&this.options.onComment(!0,this.input.slice(r+2,i),r,this.pos,e,this.curPosition())},St.skipLineComment=function(t){for(var e=this.pos,r=this.options.onComment&&this.curPosition(),i=this.input.charCodeAt(this.pos+=t);this.pos<this.input.length&&!C(i);)i=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(e+t,this.pos),e,this.pos,r,this.curPosition())},St.skipSpace=function(){t:for(;this.pos<this.input.length;){var t=this.input.charCodeAt(this.pos);switch(t){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break t}break;default:if(!(t>8&&t<14||t>=5760&&E.test(String.fromCharCode(t))))break t;++this.pos}}},St.finishToken=function(t,e){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var r=this.type;this.type=t,this.value=e,this.updateContext(r)},St.readToken_dot=function(){var t=this.input.charCodeAt(this.pos+1);if(t>=48&&t<=57)return this.readNumber(!0);var e=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===t&&46===e?(this.pos+=3,this.finishToken(w.ellipsis)):(++this.pos,this.finishToken(w.dot))},St.readToken_slash=function(){var t=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===t?this.finishOp(w.assign,2):this.finishOp(w.slash,1)},St.readToken_mult_modulo_exp=function(t){var e=this.input.charCodeAt(this.pos+1),r=1,i=42===t?w.star:w.modulo;return this.options.ecmaVersion>=7&&42===t&&42===e&&(++r,i=w.starstar,e=this.input.charCodeAt(this.pos+2)),61===e?this.finishOp(w.assign,r+1):this.finishOp(i,r)},St.readToken_pipe_amp=function(t){var e=this.input.charCodeAt(this.pos+1);return e===t?this.finishOp(124===t?w.logicalOR:w.logicalAND,2):61===e?this.finishOp(w.assign,2):this.finishOp(124===t?w.bitwiseOR:w.bitwiseAND,1)},St.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(w.assign,2):this.finishOp(w.bitwiseXOR,1)},St.readToken_plus_min=function(t){var e=this.input.charCodeAt(this.pos+1);return e===t?45!==e||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!_.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(w.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===e?this.finishOp(w.assign,2):this.finishOp(w.plusMin,1)},St.readToken_lt_gt=function(t){var e=this.input.charCodeAt(this.pos+1),r=1;return e===t?(r=62===t&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+r)?this.finishOp(w.assign,r+1):this.finishOp(w.bitShift,r)):33!==e||60!==t||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===e&&(r=2),this.finishOp(w.relational,r)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},St.readToken_eq_excl=function(t){var e=this.input.charCodeAt(this.pos+1);return 61===e?this.finishOp(w.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===t&&62===e&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(w.arrow)):this.finishOp(61===t?w.eq:w.prefix,1)},St.getTokenFromCode=function(t){switch(t){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(w.parenL);case 41:return++this.pos,this.finishToken(w.parenR);case 59:return++this.pos,this.finishToken(w.semi);case 44:return++this.pos,this.finishToken(w.comma);case 91:return++this.pos,this.finishToken(w.bracketL);case 93:return++this.pos,this.finishToken(w.bracketR);case 123:return++this.pos,this.finishToken(w.braceL);case 125:return++this.pos,this.finishToken(w.braceR);case 58:return++this.pos,this.finishToken(w.colon);case 63:return++this.pos,this.finishToken(w.question);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(w.backQuote);case 48:var e=this.input.charCodeAt(this.pos+1);if(120===e||88===e)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===e||79===e)return this.readRadixNumber(8);if(98===e||66===e)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(t);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(t);case 124:case 38:return this.readToken_pipe_amp(t);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(t);case 60:case 62:return this.readToken_lt_gt(t);case 61:case 33:return this.readToken_eq_excl(t);case 126:return this.finishOp(w.prefix,1)}this.raise(this.pos,"Unexpected character '"+Pt(t)+"'")},St.finishOp=function(t,e){var r=this.input.slice(this.pos,this.pos+e);return this.pos+=e,this.finishToken(t,r)},St.readRegexp=function(){for(var t,e,r=this.pos;;){this.pos>=this.input.length&&this.raise(r,"Unterminated regular expression");var i=this.input.charAt(this.pos);if(_.test(i)&&this.raise(r,"Unterminated regular expression"),t)t=!1;else{if("["===i)e=!0;else if("]"===i&&e)e=!1;else if("/"===i&&!e)break;t="\\"===i}++this.pos}var n=this.input.slice(r,this.pos);++this.pos;var s=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(s);var o=this.regexpState||(this.regexpState=new gt(this));o.reset(r,n,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var c=null;try{c=new RegExp(n,a)}catch(t){}return this.finishToken(w.regexp,{pattern:n,flags:a,value:c})},St.readInt=function(t,e){for(var r=this.pos,i=0,n=0,s=null==e?1/0:e;n<s;++n){var a,o=this.input.charCodeAt(this.pos);if((a=o>=97?o-97+10:o>=65?o-65+10:o>=48&&o<=57?o-48:1/0)>=t)break;++this.pos,i=i*t+a}return this.pos===r||null!=e&&this.pos-r!==e?null:i},St.readRadixNumber=function(t){var e=this.pos;this.pos+=2;var r=this.readInt(t);return null==r&&this.raise(this.start+2,"Expected number in radix "+t),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(r="undefined"!=typeof BigInt?BigInt(this.input.slice(e,this.pos)):null,++this.pos):d(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(w.num,r)},St.readNumber=function(t){var e=this.pos;t||null!==this.readInt(10)||this.raise(e,"Invalid number");var r=this.pos-e>=2&&48===this.input.charCodeAt(e);r&&this.strict&&this.raise(e,"Invalid number"),r&&/[89]/.test(this.input.slice(e,this.pos))&&(r=!1);var i=this.input.charCodeAt(this.pos);if(!r&&!t&&this.options.ecmaVersion>=11&&110===i){var n=this.input.slice(e,this.pos),s="undefined"!=typeof BigInt?BigInt(n):null;return++this.pos,d(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(w.num,s)}46!==i||r||(++this.pos,this.readInt(10),i=this.input.charCodeAt(this.pos)),69!==i&&101!==i||r||(43!==(i=this.input.charCodeAt(++this.pos))&&45!==i||++this.pos,null===this.readInt(10)&&this.raise(e,"Invalid number")),d(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var a=this.input.slice(e,this.pos),o=r?parseInt(a,8):parseFloat(a);return this.finishToken(w.num,o)},St.readCodePoint=function(){var t;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var e=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,t>1114111&&this.invalidStringToken(e,"Code point out of bounds")}else t=this.readHexChar(4);return t},St.readString=function(t){for(var e="",r=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var i=this.input.charCodeAt(this.pos);if(i===t)break;92===i?(e+=this.input.slice(r,this.pos),e+=this.readEscapedChar(!1),r=this.pos):(C(i,this.options.ecmaVersion>=10)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return e+=this.input.slice(r,this.pos++),this.finishToken(w.string,e)};var Tt={};St.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(t){if(t!==Tt)throw t;this.readInvalidTemplateToken()}this.inTemplateElement=!1},St.invalidStringToken=function(t,e){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Tt;this.raise(t,e)},St.readTmplToken=function(){for(var t="",e=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var r=this.input.charCodeAt(this.pos);if(96===r||36===r&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==w.template&&this.type!==w.invalidTemplate?(t+=this.input.slice(e,this.pos),this.finishToken(w.template,t)):36===r?(this.pos+=2,this.finishToken(w.dollarBraceL)):(++this.pos,this.finishToken(w.backQuote));if(92===r)t+=this.input.slice(e,this.pos),t+=this.readEscapedChar(!0),e=this.pos;else if(C(r)){switch(t+=this.input.slice(e,this.pos),++this.pos,r){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:t+="\n";break;default:t+=String.fromCharCode(r)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),e=this.pos}else++this.pos}},St.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(w.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},St.readEscapedChar=function(t){var e=this.input.charCodeAt(++this.pos);switch(++this.pos,e){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return Pt(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";default:if(e>=48&&e<=55){var r=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],i=parseInt(r,8);return i>255&&(r=r.slice(0,-1),i=parseInt(r,8)),this.pos+=r.length-1,e=this.input.charCodeAt(this.pos),"0"===r&&56!==e&&57!==e||!this.strict&&!t||this.invalidStringToken(this.pos-1-r.length,t?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(i)}return C(e)?"":String.fromCharCode(e)}},St.readHexChar=function(t){var e=this.pos,r=this.readInt(16,t);return null===r&&this.invalidStringToken(e,"Bad character escape sequence"),r},St.readWord1=function(){this.containsEsc=!1;for(var t="",e=!0,r=this.pos,i=this.options.ecmaVersion>=6;this.pos<this.input.length;){var n=this.fullCharCodeAtPos();if(m(n,i))this.pos+=n<=65535?1:2;else{if(92!==n)break;this.containsEsc=!0,t+=this.input.slice(r,this.pos);var s=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(e?d:m)(a,i)||this.invalidStringToken(s,"Invalid Unicode escape"),t+=Pt(a),r=this.pos}e=!1}return t+this.input.slice(r,this.pos)},St.readWord=function(){var t=this.readWord1(),e=w.name;return this.keywords.test(t)&&(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+t),e=b[t]),this.finishToken(e,t)};var It="7.1.0";function Ot(t,e){return D.parse(t,e)}function Nt(t,e,r){return D.parseExpressionAt(t,e,r)}function Lt(t,e){return D.tokenizer(t,e)}D.acorn={Parser:D,version:It,defaultOptions:B,Position:j,SourceLocation:V,getLineInfo:R,Node:rt,TokenType:g,tokTypes:w,keywordTypes:b,TokContext:st,tokContexts:at,isIdentifierChar:m,isIdentifierStart:d,Token:Et,isNewLine:C,lineBreak:_,lineBreakG:A,nonASCIIwhitespace:E}},function(t,e,r){"use strict";var i=r(3),n=/^[\da-fA-F]+$/,h=/^\d+$/,u=new WeakMap;function l(t){t=t.Parser.acorn||t;var e=u.get(t);if(!e){var r=t.tokTypes,i=t.TokContext,n=t.TokenType,s=new i("<tag",!1),a=new i("</tag",!1),o=new i("<tag>...</tag>",!0,!0),c={tc_oTag:s,tc_cTag:a,tc_expr:o},h={jsxName:new n("jsxName"),jsxText:new n("jsxText",{beforeExpr:!0}),jsxTagStart:new n("jsxTagStart"),jsxTagEnd:new n("jsxTagEnd")};h.jsxTagStart.updateContext=function(){this.context.push(o),this.context.push(s),this.exprAllowed=!1},h.jsxTagEnd.updateContext=function(t){var e=this.context.pop();e===s&&t===r.slash||e===a?(this.context.pop(),this.exprAllowed=this.curContext()===o):this.exprAllowed=!0},e={tokContexts:c,tokTypes:h},u.set(t,e)}return e}function p(t){return t?"JSXIdentifier"===t.type?t.name:"JSXNamespacedName"===t.type?t.namespace.name+":"+t.name.name:"JSXMemberExpression"===t.type?p(t.object)+"."+p(t.property):void 0:t}t.exports=function(t){return t=t||{},function(e){return function(t,e){var u=e.acorn||r(1),f=l(u),d=u.tokTypes,m=f.tokTypes,g=u.tokContexts,v=f.tokContexts.tc_oTag,x=f.tokContexts.tc_cTag,y=f.tokContexts.tc_expr,b=u.isNewLine,k=u.isIdentifierStart,w=u.isIdentifierChar;return function(e){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&o(t,e)}(E,e);var r,u,l,_,A,C=(_=E,A=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=c(_);if(A){var r=c(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function E(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),C.apply(this,arguments)}return r=E,u=[{key:"jsx_readToken",value:function(){for(var t="",e=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated JSX contents");var r=this.input.charCodeAt(this.pos);switch(r){case 60:case 123:return this.pos===this.start?60===r&&this.exprAllowed?(++this.pos,this.finishToken(m.jsxTagStart)):this.getTokenFromCode(r):(t+=this.input.slice(e,this.pos),this.finishToken(m.jsxText,t));case 38:t+=this.input.slice(e,this.pos),t+=this.jsx_readEntity(),e=this.pos;break;default:b(r)?(t+=this.input.slice(e,this.pos),t+=this.jsx_readNewLine(!0),e=this.pos):++this.pos}}}},{key:"jsx_readNewLine",value:function(t){var e,r=this.input.charCodeAt(this.pos);return++this.pos,13===r&&10===this.input.charCodeAt(this.pos)?(++this.pos,e=t?"\n":"\r\n"):e=String.fromCharCode(r),this.options.locations&&(++this.curLine,this.lineStart=this.pos),e}},{key:"jsx_readString",value:function(t){for(var e="",r=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var i=this.input.charCodeAt(this.pos);if(i===t)break;38===i?(e+=this.input.slice(r,this.pos),e+=this.jsx_readEntity(),r=this.pos):b(i)?(e+=this.input.slice(r,this.pos),e+=this.jsx_readNewLine(!1),r=this.pos):++this.pos}return e+=this.input.slice(r,this.pos++),this.finishToken(d.string,e)}},{key:"jsx_readEntity",value:function(){var t,e="",r=0,s=this.input[this.pos];"&"!==s&&this.raise(this.pos,"Entity must start with an ampersand");for(var a=++this.pos;this.pos<this.input.length&&r++<10;){if(";"===(s=this.input[this.pos++])){"#"===e[0]?"x"===e[1]?(e=e.substr(2),n.test(e)&&(t=String.fromCharCode(parseInt(e,16)))):(e=e.substr(1),h.test(e)&&(t=String.fromCharCode(parseInt(e,10)))):t=i[e];break}e+=s}return t||(this.pos=a,"&")}},{key:"jsx_readWord",value:function(){var t,e=this.pos;do{t=this.input.charCodeAt(++this.pos)}while(w(t)||45===t);return this.finishToken(m.jsxName,this.input.slice(e,this.pos))}},{key:"jsx_parseIdentifier",value:function(){var t=this.startNode();return this.type===m.jsxName?t.name=this.value:this.type.keyword?t.name=this.type.keyword:this.unexpected(),this.next(),this.finishNode(t,"JSXIdentifier")}},{key:"jsx_parseNamespacedName",value:function(){var e=this.start,r=this.startLoc,i=this.jsx_parseIdentifier();if(!t.allowNamespaces||!this.eat(d.colon))return i;var n=this.startNodeAt(e,r);return n.namespace=i,n.name=this.jsx_parseIdentifier(),this.finishNode(n,"JSXNamespacedName")}},{key:"jsx_parseElementName",value:function(){if(this.type===m.jsxTagEnd)return"";var e=this.start,r=this.startLoc,i=this.jsx_parseNamespacedName();for(this.type!==d.dot||"JSXNamespacedName"!==i.type||t.allowNamespacedObjects||this.unexpected();this.eat(d.dot);){var n=this.startNodeAt(e,r);n.object=i,n.property=this.jsx_parseIdentifier(),i=this.finishNode(n,"JSXMemberExpression")}return i}},{key:"jsx_parseAttributeValue",value:function(){switch(this.type){case d.braceL:var t=this.jsx_parseExpressionContainer();return"JSXEmptyExpression"===t.expression.type&&this.raise(t.start,"JSX attributes must only be assigned a non-empty expression"),t;case m.jsxTagStart:case d.string:return this.parseExprAtom();default:this.raise(this.start,"JSX value should be either an expression or a quoted JSX text")}}},{key:"jsx_parseEmptyExpression",value:function(){var t=this.startNodeAt(this.lastTokEnd,this.lastTokEndLoc);return this.finishNodeAt(t,"JSXEmptyExpression",this.start,this.startLoc)}},{key:"jsx_parseExpressionContainer",value:function(){var t=this.startNode();return this.next(),t.expression=this.type===d.braceR?this.jsx_parseEmptyExpression():this.parseExpression(),this.expect(d.braceR),this.finishNode(t,"JSXExpressionContainer")}},{key:"jsx_parseAttribute",value:function(){var t=this.startNode();return this.eat(d.braceL)?(this.expect(d.ellipsis),t.argument=this.parseMaybeAssign(),this.expect(d.braceR),this.finishNode(t,"JSXSpreadAttribute")):(t.name=this.jsx_parseNamespacedName(),t.value=this.eat(d.eq)?this.jsx_parseAttributeValue():null,this.finishNode(t,"JSXAttribute"))}},{key:"jsx_parseOpeningElementAt",value:function(t,e){var r=this.startNodeAt(t,e);r.attributes=[];var i=this.jsx_parseElementName();for(i&&(r.name=i);this.type!==d.slash&&this.type!==m.jsxTagEnd;)r.attributes.push(this.jsx_parseAttribute());return r.selfClosing=this.eat(d.slash),this.expect(m.jsxTagEnd),this.finishNode(r,i?"JSXOpeningElement":"JSXOpeningFragment")}},{key:"jsx_parseClosingElementAt",value:function(t,e){var r=this.startNodeAt(t,e),i=this.jsx_parseElementName();return i&&(r.name=i),this.expect(m.jsxTagEnd),this.finishNode(r,i?"JSXClosingElement":"JSXClosingFragment")}},{key:"jsx_parseElementAt",value:function(t,e){var r=this.startNodeAt(t,e),i=[],n=this.jsx_parseOpeningElementAt(t,e),s=null;if(!n.selfClosing){t:for(;;)switch(this.type){case m.jsxTagStart:if(t=this.start,e=this.startLoc,this.next(),this.eat(d.slash)){s=this.jsx_parseClosingElementAt(t,e);break t}i.push(this.jsx_parseElementAt(t,e));break;case m.jsxText:i.push(this.parseExprAtom());break;case d.braceL:i.push(this.jsx_parseExpressionContainer());break;default:this.unexpected()}p(s.name)!==p(n.name)&&this.raise(s.start,"Expected corresponding JSX closing tag for <"+p(n.name)+">")}var a=n.name?"Element":"Fragment";return r["opening"+a]=n,r["closing"+a]=s,r.children=i,this.type===d.relational&&"<"===this.value&&this.raise(this.start,"Adjacent JSX elements must be wrapped in an enclosing tag"),this.finishNode(r,"JSX"+a)}},{key:"jsx_parseText",value:function(t){var e=this.parseLiteral(t);return e.type="JSXText",e}},{key:"jsx_parseElement",value:function(){var t=this.start,e=this.startLoc;return this.next(),this.jsx_parseElementAt(t,e)}},{key:"parseExprAtom",value:function(t){return this.type===m.jsxText?this.jsx_parseText(this.value):this.type===m.jsxTagStart?this.jsx_parseElement():a(c(E.prototype),"parseExprAtom",this).call(this,t)}},{key:"readToken",value:function(t){var e=this.curContext();if(e===y)return this.jsx_readToken();if(e===v||e===x){if(k(t))return this.jsx_readWord();if(62==t)return++this.pos,this.finishToken(m.jsxTagEnd);if((34===t||39===t)&&e==v)return this.jsx_readString(t)}return 60===t&&this.exprAllowed&&33!==this.input.charCodeAt(this.pos+1)?(++this.pos,this.finishToken(m.jsxTagStart)):a(c(E.prototype),"readToken",this).call(this,t)}},{key:"updateContext",value:function(t){if(this.type==d.braceL){var e=this.curContext();e==v?this.context.push(g.b_expr):e==y?this.context.push(g.b_tmpl):a(c(E.prototype),"updateContext",this).call(this,t),this.exprAllowed=!0}else{if(this.type!==d.slash||t!==m.jsxTagStart)return a(c(E.prototype),"updateContext",this).call(this,t);this.context.length-=2,this.context.push(x),this.exprAllowed=!1}}}],l=[{key:"acornJsx",get:function(){return f}}],u&&s(r.prototype,u),l&&s(r,l),Object.defineProperty(r,"prototype",{writable:!1}),E}(e)}({allowNamespaces:!1!==t.allowNamespaces,allowNamespacedObjects:!!t.allowNamespacedObjects},e)}},Object.defineProperty(t.exports,"tokTypes",{get:function(){return l(r(1)).tokTypes},configurable:!0,enumerable:!0})},function(t,e){t.exports={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}},function(t,e,r){"use strict";r.r(e);var i=r(1),s=r(2),a=r.n(s),o=r(0),c=r.n(o),h={class:"className",for:"htmlFor",maxlength:"maxLength",colspan:"colSpan",rowspan:"rowSpan"},u=["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr"],l=["table","tbody","tfoot","thead","tr"],p=function(){return function(){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,e=String(arguments.length>0&&void 0!==arguments[0]?arguments[0]:""),r=0;return e.split("").forEach((function(t){r=(r<<5)-r+t.charCodeAt(0),r&=r})),Math.abs(r).toString(t)}(Math.random().toString())};function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function d(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t){switch(m(t)){case"string":return t.split(";").filter((function(t){return t})).reduce((function(t,e){var r=e.slice(0,e.indexOf(":")).trim(),i=e.slice(e.indexOf(":")+1).trim();return function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(r,!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(r).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t,d({},r.replace(/([A-Z])([A-Z])/g,"$1 $2").replace(/([a-z])([A-Z])/g,"$1 $2").replace(/[^a-zA-Z\u00C0-\u00ff]/g," ").toLowerCase().split(" ").filter((function(t){return t})).map((function(t,e){return e>0?t[0].toUpperCase()+t.slice(1):t})).join(""),i))}),{});case"object":return t;default:return}}var v=function(t,e){return function t(e,r){var i=function(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}(r),n=i[0],s=i.slice(1);if(null!=e&&null!=n)return 0===s.length?e[n]:t(e[n],s)}(t,function(t){return null==t||""===t?[]:t.split(".")}(e))};function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function b(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function k(t,e){return(k=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function w(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}r.d(e,"default",(function(){return A}));var _=i.Parser.extend(a()()),A=function(t){function e(){var t,r,i;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var s=arguments.length,a=new Array(s),f=0;f<s;f++)a[f]=arguments[f];return this,i=(t=y(e)).call.apply(t,[this].concat(a)),r=!i||"object"!==x(i)&&"function"!=typeof i?b(this):i,w(b(r),"parseJSX",(function(t){var e="<root>".concat(t,"</root>"),i=[];try{i=(i=_.parse(e)).body[0].expression.children||[]}catch(t){return r.props.showWarnings&&n.warn(t),r.props.onError&&r.props.onError(t),r.props.renderError?r.props.renderError({error:String(t)}):[]}return i.map(r.parseExpression).filter(Boolean)})),w(b(r),"parseExpression",(function(t){switch(t.type){case"JSXAttribute":return null===t.value||r.parseExpression(t.value);case"JSXElement":return r.parseElement(t);case"JSXExpressionContainer":return r.parseExpression(t.expression);case"JSXText":var e=r.props.disableKeyGeneration?void 0:p();return r.props.disableFragments?t.value:c.a.createElement(o.Fragment,{key:e},t.value);case"ArrayExpression":return t.elements.map(r.parseExpression);case"BinaryExpression":switch(t.operator){case"-":return r.parseExpression(t.left)-r.parseExpression(t.right);case"!=":return r.parseExpression(t.left)!=r.parseExpression(t.right);case"!==":return r.parseExpression(t.left)!==r.parseExpression(t.right);case"*":return r.parseExpression(t.left)*r.parseExpression(t.right);case"**":return Math.pow(r.parseExpression(t.left),r.parseExpression(t.right));case"/":return r.parseExpression(t.left)/r.parseExpression(t.right);case"%":return r.parseExpression(t.left)%r.parseExpression(t.right);case"+":return r.parseExpression(t.left)+r.parseExpression(t.right);case"==":return r.parseExpression(t.left)==r.parseExpression(t.right);case"===":return r.parseExpression(t.left)===r.parseExpression(t.right)}return;case"CallExpression":var i=r.parseExpression(t.callee);return void 0===i?void r.props.onError(new Error("The expression '".concat(t.callee,"' could not be resolved, resulting in an undefined return value."))):i.apply(void 0,function(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}(t.arguments.map(r.parseExpression)));case"ConditionalExpression":return r.parseExpression(t.test)?r.parseExpression(t.consequent):r.parseExpression(t.alternate);case"Identifier":return(r.props.bindings||{})[t.name];case"Literal":return t.value;case"LogicalExpression":var n=r.parseExpression(t.left);return"||"===t.operator&&n?n:!!("&&"===t.operator&&n||"||"===t.operator&&!n)&&r.parseExpression(t.right);case"MemberExpression":var s=r.parseExpression(t.object)||{},a=s[t.property.name];return"function"==typeof a?a.bind(s):a;case"ObjectExpression":var h={};return t.properties.forEach((function(t){h[t.key.name||t.key.value]=r.parseExpression(t.value)})),h;case"UnaryExpression":switch(t.operator){case"+":return t.argument.value;case"-":return-1*t.argument.value;case"!":return!t.argument.value}return}})),w(b(r),"parseName",(function(t){switch(t.type){case"JSXIdentifier":return t.name;case"JSXMemberExpression":return"".concat(r.parseName(t.object),".").concat(r.parseName(t.property))}})),w(b(r),"parseElement",(function(t){var e=r.props,i=e.allowUnknownElements,n=e.components,s=void 0===n?{}:n,a=e.componentsOnly,o=e.onError,f=t.children,d=void 0===f?[]:f,m=t.openingElement,y=m.attributes,b=void 0===y?[]:y,k=r.parseName(m.name);if(k){var w=(r.props.blacklistedAttrs||[]).map((function(t){return t instanceof RegExp?t:new RegExp(t,"i")})),_=(r.props.blacklistedTags||[]).map((function(t){return t.trim().toLowerCase()})).filter(Boolean);if(/^(html|head|body)$/i.test(k))return d.map((function(t){return r.parseElement(t)}));var A=k.trim().toLowerCase();if(-1===_.indexOf(A)){if(!v(s,k)){if(a)return void o(new Error("The componenet <".concat(k,"> is unrecognized, and will not be rendered.")));if(!i&&document.createElement(k)instanceof HTMLUnknownElement)return void o(new Error("The tag <".concat(k,"> is unrecognized in this browser, and will not be rendered.")))}var C,E=v(s,k);(E||function(t){return-1===u.indexOf(t.toLowerCase())}(k))&&(C=d.map(r.parseExpression),E||function(t){return-1!==l.indexOf(t.toLowerCase())}(k)||(C=C.filter((function(t){return"string"!=typeof t||!/^\s*$/.test(t)}))),0===C.length?C=void 0:1===C.length&&(C=function(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t)){var r=[],i=!0,n=!1,s=void 0;try{for(var a,o=t[Symbol.iterator]();!(i=(a=o.next()).done)&&(r.push(a.value),!e||r.length!==e);i=!0);}catch(t){n=!0,s=t}finally{try{i||null==o.return||o.return()}finally{if(n)throw s}}return r}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}(C,1)[0]));var S={key:r.props.disableKeyGeneration?void 0:p()};return b.forEach((function(t){if("JSXAttribute"===t.type){var e=t.name.name,i=h[e]||e,n=r.parseExpression(t);0===w.filter((function(t){return t.test(i)})).length&&(S[i]="true"===n||"false"===n?"true"===n:n)}else if("JSXSpreadAttribute"===t.type&&"Identifier"===t.argument.type||"MemberExpression"===t.argument.type){var s=r.parseExpression(t.argument);"object"===x(s)&&Object.keys(s).forEach((function(t){var e=h[t]||t;0===w.filter((function(t){return t.test(e)})).length&&(S[e]=s[t])}))}})),"string"==typeof S.style&&(S.style=g(S.style)),C&&(S.children=C),c.a.createElement(E||k.toLowerCase(),S)}o(new Error("The tag <".concat(k,"> is blacklisted, and will not be rendered.")))}else o(new Error("The <".concat(m.name,"> tag could not be parsed, and will not be rendered.")))})),w(b(r),"render",(function(){var t=(r.props.jsx||"").trim().replace(/<!DOCTYPE([^>]*)>/g,"");return r.ParsedChildren=r.parseJSX(t),r.props.renderInWrapper?c.a.createElement("div",{className:"jsx-parser"},r.ParsedChildren):c.a.createElement(c.a.Fragment,null,r.ParsedChildren)})),r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&k(t,e)}(e,t),e}(o.Component);w(A,"displayName","JsxParser"),w(A,"defaultProps",{allowUnknownElements:!0,bindings:{},blacklistedAttrs:[/^on.+/i],blacklistedTags:["script"],components:[],componentsOnly:!1,disableFragments:!1,disableKeyGeneration:!1,jsx:"",onError:function(){},showWarnings:!1,renderError:void 0,renderInWrapper:!0})}]))},70874:function(t,e,r){"use strict";var i=r(99196),n=r(47529),s=r(59864),a={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function o(t,e){for(var r in e)void 0!==e[r]&&(t[r]=e[r])}t.exports=function t(e,r){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},h=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,u=r.renderers[e.type];void 0===e.position&&(e.position=c.node&&c.node.position||a);var l=e.position.start,p=[e.type,l.line,l.column,h].join("-");if(!s.isValidElementType(u))throw new Error("Renderer for type `".concat(e.type,"` not defined or is not renderable"));var f=function(e,r,s,a,c,h){var u={key:r},l="string"==typeof a;s.sourcePos&&e.position&&(u["data-sourcepos"]=function(t){return[t.start.line,":",t.start.column,"-",t.end.line,":",t.end.column].map(String).join("")}(e.position)),s.rawSourcePos&&!l&&(u.sourcePosition=e.position),s.includeNodeIndex&&c.node&&c.node.children&&!l&&(u.index=c.node.children.indexOf(e),u.parentChildCount=c.node.children.length);var p=null!==e.identifier&&void 0!==e.identifier?s.definitions[e.identifier]||{}:null;switch(e.type){case"root":o(u,{className:s.className});break;case"text":u.nodeKey=r,u.children=e.value;break;case"heading":u.level=e.depth;break;case"list":u.start=e.start,u.ordered=e.ordered,u.tight=!e.loose,u.depth=e.depth;break;case"listItem":u.checked=e.checked,u.tight=!e.loose,u.ordered=e.ordered,u.index=e.index,u.children=function(t,e){return t.loose||e.node&&t.index>0&&e.node.children[t.index-1].loose?t.children:function(t){return t.children.reduce((function(t,e){return t.concat("paragraph"===e.type?e.children||[]:[e])}),[])}(t)}(e,c).map((function(r,i){return t(r,s,{node:e,props:u},i)}));break;case"definition":o(u,{identifier:e.identifier,title:e.title,url:e.url});break;case"code":o(u,{language:e.lang&&e.lang.split(/\s/,1)[0]});break;case"inlineCode":u.children=e.value,u.inline=!0;break;case"link":o(u,{title:e.title||void 0,target:"function"==typeof s.linkTarget?s.linkTarget(e.url,e.children,e.title):s.linkTarget,href:s.transformLinkUri?s.transformLinkUri(e.url,e.children,e.title):e.url});break;case"image":o(u,{alt:e.alt||void 0,title:e.title||void 0,src:s.transformImageUri?s.transformImageUri(e.url,e.children,e.title,e.alt):e.url});break;case"linkReference":o(u,n(p,{href:s.transformLinkUri?s.transformLinkUri(p.href):p.href}));break;case"imageReference":o(u,{src:s.transformImageUri&&p.href?s.transformImageUri(p.href,e.children,p.title,e.alt):p.href,title:p.title||void 0,alt:e.alt||void 0});break;case"table":case"tableHead":case"tableBody":u.columnAlignment=e.align;break;case"tableRow":u.isHeader="tableHead"===c.node.type,u.columnAlignment=c.props.columnAlignment;break;case"tableCell":o(u,{isHeader:c.props.isHeader,align:c.props.columnAlignment[h]});break;case"virtualHtml":u.tag=e.tag;break;case"html":u.isBlock=e.position.start.line!==e.position.end.line,u.escapeHtml=s.escapeHtml,u.skipHtml=s.skipHtml;break;case"parsedHtml":var f;e.children&&(f=e.children.map((function(r,i){return t(r,s,{node:e,props:u},i)}))),u.escapeHtml=s.escapeHtml,u.skipHtml=s.skipHtml,u.element=function(t,e){var r=t.element;if(Array.isArray(r)){var n=i.Fragment||"div";return i.createElement(n,null,r)}if(r.props.children||e){var s=i.Children.toArray(r.props.children).concat(e);return i.cloneElement(r,null,s)}return i.cloneElement(r,null)}(e,f);break;default:o(u,n(e,{type:void 0,position:void 0,children:void 0}))}return!l&&e.value&&(u.value=e.value),u}(e,p,r,u,c,h);return i.createElement(u,f,f.children||e.children&&e.children.map((function(i,n){return t(i,r,{node:e,props:f},n)}))||void 0)}},86738:function(t){"use strict";t.exports=function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(e.children||[]).reduce((function(e,r){return"definition"===r.type&&(e[r.identifier]={href:r.url,title:r.title}),t(r,e)}),r)}},27767:function(t,e,r){"use strict";var i=r(62854);function n(t,e,r,i){if("remove"===i)r.children.splice(e,1);else if("unwrap"===i){var n=[e,1];t.children&&(n=n.concat(t.children)),Array.prototype.splice.apply(r.children,n)}}e.ofType=function(t,e){return function(e){return t.forEach((function(t){return i(e,t,r,!0)})),e};function r(t,r,i){i&&n(t,r,i,e)}},e.ifNotMatch=function(t,e){return function(t){return i(t,r,!0),t};function r(r,i,s){s&&!t(r,i,s)&&n(r,i,s,e)}}},21579:function(t,e,r){"use strict";var i=r(62854),n="virtualHtml",s=/^<(area|base|br|col|embed|hr|img|input|keygen|link|meta|param|source|track|wbr)\s*\/?>$/i,a=/^<(\/?)([a-z]+)\s*>$/;t.exports=function(t){var e,r;return i(t,"html",(function(t,i,o){r!==o&&(e=[],r=o);var c=function(t){var e=t.value.match(s);return!!e&&e[1]}(t);if(c)return o.children.splice(i,1,{type:n,tag:c,position:t.position}),!0;var h=function(t,e){var r=t.value.match(a);return!!r&&{tag:r[2],opening:!r[1],node:t}}(t);if(!h)return!0;var u=function(t,e){for(var r=t.length;r--;)if(t[r].tag===e)return t.splice(r,1)[0];return!1}(e,h.tag);return u?o.children.splice(i,0,function(t,e,r){var i=r.children.indexOf(t.node),s=r.children.indexOf(e.node),a=r.children.splice(i,s-i+1).slice(1,-1);return{type:n,children:a,tag:t.tag,position:{start:t.node.position.start,end:e.node.position.end,indent:[]}}}(h,u,o)):h.opening||e.push(h),!0}),!0),t}},94838:function(t,e,r){"use strict";var i=r(47529),n=r(18835),s=r(12861),a=r(69064),o=r(36522),c=r(21579),h=r(27767),u=r(70874),l=r(48046),p=r(86738),f=r(54388),d=r(11339),m=r(87948),g=Object.keys(d),v=function(t){var e=t.source||t.children||"",r=t.parserOptions;if(t.allowedTypes&&t.disallowedTypes)throw new Error("Only one of `allowedTypes` and `disallowedTypes` should be defined");var a=i(d,t.renderers),f=[[s,r]].concat(t.plugins||[]).reduce(x,n()),v=f.parse(e),y=i(t,{renderers:a,definitions:p(v)}),b=function(t){var e=[l,o()],r=t.disallowedTypes;t.allowedTypes&&(r=g.filter((function(e){return"root"!==e&&-1===t.allowedTypes.indexOf(e)})));var i=t.unwrapDisallowed?"unwrap":"remove";r&&r.length>0&&e.push(h.ofType(r,i)),t.allowNode&&e.push(h.ifNotMatch(t.allowNode,i));var n=!t.escapeHtml&&!t.skipHtml,s=(t.astPlugins||[]).some((function(t){return(Array.isArray(t)?t[0]:t).identity===m.HtmlParser}));return n&&!s&&e.push(c),t.astPlugins?e.concat(t.astPlugins):e}(t),k=f.runSync(v),w=b.reduce((function(t,e){return e(t,y)}),k);return u(w,y)};function x(t,e){return Array.isArray(e)?t.use.apply(t,function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(r=e)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()):t.use(e);var r}v.defaultProps={renderers:{},escapeHtml:!0,skipHtml:!1,sourcePos:!1,rawSourcePos:!1,transformLinkUri:f,astPlugins:[],plugins:[],parserOptions:{}},v.propTypes={className:a.string,source:a.string,children:a.string,sourcePos:a.bool,rawSourcePos:a.bool,escapeHtml:a.bool,skipHtml:a.bool,allowNode:a.func,allowedTypes:a.arrayOf(a.oneOf(g)),disallowedTypes:a.arrayOf(a.oneOf(g)),transformLinkUri:a.oneOfType([a.func,a.bool]),linkTarget:a.oneOfType([a.func,a.string]),transformImageUri:a.func,astPlugins:a.arrayOf(a.func),unwrapDisallowed:a.bool,renderers:a.object,plugins:a.array,parserOptions:a.object},v.types=g,v.renderers=d,v.uriTransformer=f,t.exports=v},11339:function(t,e,r){"use strict";var i=r(47529),n=r(99196),s=parseInt((n.version||"16").slice(0,2),10)>=16,a=n.createElement;function o(t,e){return a(t,c(e),e.children)}function c(t){return t["data-sourcepos"]?{"data-sourcepos":t["data-sourcepos"]}:{}}t.exports={break:"br",paragraph:"p",emphasis:"em",strong:"strong",thematicBreak:"hr",blockquote:"blockquote",delete:"del",link:"a",image:"img",linkReference:"a",imageReference:"img",table:o.bind(null,"table"),tableHead:o.bind(null,"thead"),tableBody:o.bind(null,"tbody"),tableRow:o.bind(null,"tr"),tableCell:function(t){var e=t.align?{textAlign:t.align}:void 0,r=c(t);return a(t.isHeader?"th":"td",e?i({style:e},r):r,t.children)},root:function(t){var e=!t.className,r=e&&n.Fragment||"div";return a(r,e?null:t,t.children)},text:function(t){return s?t.children:a("span",null,t.children)},list:function(t){var e=c(t);return null!==t.start&&1!==t.start&&void 0!==t.start&&(e.start=t.start.toString()),a(t.ordered?"ol":"ul",e,t.children)},listItem:function(t){var e=null;if(null!==t.checked&&void 0!==t.checked){var r=t.checked;e=a("input",{type:"checkbox",checked:r,readOnly:!0})}return a("li",c(t),e,t.children)},definition:function(){return null},heading:function(t){return a("h".concat(t.level),c(t),t.children)},inlineCode:function(t){return a("code",c(t),t.children)},code:function(t){var e=t.language&&"language-".concat(t.language),r=a("code",e?{className:e}:null,t.value);return a("pre",c(t),r)},html:function(t){if(t.skipHtml)return null;var e=t.isBlock?"div":"span";if(t.escapeHtml){var r=n.Fragment||e;return a(r,null,t.value)}var i={dangerouslySetInnerHTML:{__html:t.value}};return a(e,i)},virtualHtml:function(t){return a(t.tag,c(t),t.children)},parsedHtml:function(t){return t["data-sourcepos"]?n.cloneElement(t.element,{"data-sourcepos":t["data-sourcepos"]}):t.element}}},87948:function(t,e){"use strict";var r="__RMD_HTML_PARSER__";e.HtmlParser="undefined"==typeof Symbol?r:Symbol(r)},54388:function(t){"use strict";var e=["http","https","mailto","tel"];t.exports=function(t){var r=(t||"").trim(),i=r.charAt(0);if("#"===i||"/"===i)return r;var n=r.indexOf(":");if(-1===n)return r;for(var s=e.length,a=-1;++a<s;){var o=e[a];if(n===o.length&&r.slice(0,o.length).toLowerCase()===o)return r}return-1!==(a=r.indexOf("?"))&&n>a||-1!==(a=r.indexOf("#"))&&n>a?r:"javascript:void(0)"}},48046:function(t,e,r){"use strict";var i=r(62854);function n(t){var e=t.children;t.children=[{type:"tableHead",align:t.align,children:[e[0]],position:e[0].position}],e.length>1&&t.children.push({type:"tableBody",align:t.align,children:e.slice(1),position:{start:e[1].position.start,end:e[e.length-1].position.end}})}t.exports=function(t){return i(t,"table",n),t}},94999:function(t,e,r){var i=r(14790);t.exports=function(){var t=this.Parser,e=this.Compiler;i.isRemarkParser(t)&&function(t){var e=t.prototype,r=e.blockMethods,i=e.interruptParagraph,l=e.interruptList,p=e.interruptBlockquote;e.blockTokenizers.math=function(t,e,r){for(var i,l,p,f,d,m,g,v,x,y,b,k=e.length,w=0;w<k&&e.charCodeAt(w)===s;)w++;for(d=w;w<k&&e.charCodeAt(w)===a;)w++;if(!((m=w-d)<h)){for(;w<k&&e.charCodeAt(w)===s;)w++;for(g=w;w<k;){if((i=e.charCodeAt(w))===a)return;if(i===n)break;w++}if(e.charCodeAt(w)===n){if(r)return!0;for(l=[],g!==w&&l.push(e.slice(g,w)),w++,p=-1===(p=e.indexOf(o,w+1))?k:p;w<k;){for(v=!1,y=w,b=p,f=p,x=0;f>y&&e.charCodeAt(f-1)===s;)f--;for(;f>y&&e.charCodeAt(f-1)===a;)x++,f--;for(m<=x&&e.indexOf(c,y)===f&&(v=!0,b=f);y<=b&&y-w<d&&e.charCodeAt(y)===s;)y++;if(v)for(;b>y&&e.charCodeAt(b-1)===s;)b--;if(v&&y===b||l.push(e.slice(y,b)),v)break;w=p+1,p=-1===(p=e.indexOf(o,w+1))?k:p}return l=l.join("\n"),t(e.slice(0,p))({type:"math",value:l,data:{hName:"div",hProperties:{className:u.concat()},hChildren:[{type:"text",value:l}]}})}}},r.splice(r.indexOf("fencedCode")+1,0,"math"),i.splice(i.indexOf("fencedCode")+1,0,["math"]),l.splice(l.indexOf("fencedCode")+1,0,["math"]),p.splice(p.indexOf("fencedCode")+1,0,["math"])}(t),i.isRemarkCompiler(e)&&function(t){t.prototype.visitors.math=function(t){return"$$\n"+t.value+"\n$$"}}(e)};var n=10,s=32,a=36,o="\n",c="$",h=2,u=["math","math-display"]},3933:function(t,e,r){var i=r(56224),n=r(94999);t.exports=function(t){var e=t||{};n.call(this,e),i.call(this,e)}},56224:function(t,e,r){var i=r(14790);t.exports=function(t){var e=this.Parser,r=this.Compiler;i.isRemarkParser(e)&&function(t,e){var r=t.prototype,i=r.inlineMethods;function p(t,r,i){var p,f,d,m,g,v,x,y=r.length,b=!1,k=!1,w=0;if(r.charCodeAt(w)===h&&(k=!0,w++),r.charCodeAt(w)===a){if(w++,k)return!!i||t(r.slice(0,w))({type:"text",value:"$"});if(r.charCodeAt(w)===a&&(b=!0,w++),(d=r.charCodeAt(w))!==s&&d!==n){for(m=w;w<y;){if(f=d,d=r.charCodeAt(w+1),f===a){if((p=r.charCodeAt(w-1))!==s&&p!==n&&(d!=d||d<o||d>c)&&(!b||d===a)){g=w-1,w++,b&&w++,v=w;break}}else f===h&&(w++,d=r.charCodeAt(w+1));w++}if(void 0!==v)return!!i||(x=r.slice(m,g+1),t(r.slice(0,v))({type:"inlineMath",value:x,data:{hName:"span",hProperties:{className:u.concat(b&&e.inlineMathDouble?[l]:[])},hChildren:[{type:"text",value:x}]}}))}}}p.locator=function(t,e){return t.indexOf("$",e)},r.inlineTokenizers.math=p,i.splice(i.indexOf("text"),0,"math")}(e,t),i.isRemarkCompiler(r)&&function(t){t.prototype.visitors.inlineMath=function(t){var e="$";return(t.data&&t.data.hProperties&&t.data.hProperties.className||[]).includes(l)&&(e="$$"),e+t.value+e}}(r)};var n=9,s=32,a=36,o=48,c=57,h=92,u=["math","math-inline"],l="math-display"},14790:function(t,e){e.isRemarkParser=function(t){return Boolean(t&&t.prototype&&t.prototype.blockTokenizers)},e.isRemarkCompiler=function(t){return Boolean(t&&t.prototype&&t.prototype.visitors)}},18869:function(t){"use strict";t.exports=function(t){if(t)throw t}},69514:function(t){"use strict";t.exports=function(t){return String(t).replace(/\s+/g," ")}},94470:function(t){"use strict";var e=Object.prototype.hasOwnProperty,r=Object.prototype.toString,i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===r.call(t)},a=function(t){if(!t||"[object Object]"!==r.call(t))return!1;var i,n=e.call(t,"constructor"),s=t.constructor&&t.constructor.prototype&&e.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!s)return!1;for(i in t);return void 0===i||e.call(t,i)},o=function(t,e){i&&"__proto__"===e.name?i(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},c=function(t,r){if("__proto__"===r){if(!e.call(t,r))return;if(n)return n(t,r).value}return t[r]};t.exports=function t(){var e,r,i,n,h,u,l=arguments[0],p=1,f=arguments.length,d=!1;for("boolean"==typeof l&&(d=l,l=arguments[1]||{},p=2),(null==l||"object"!=typeof l&&"function"!=typeof l)&&(l={});p<f;++p)if(null!=(e=arguments[p]))for(r in e)i=c(l,r),l!==(n=c(e,r))&&(d&&n&&(a(n)||(h=s(n)))?(h?(h=!1,u=i&&s(i)?i:[]):u=i&&a(i)?i:{},o(l,{name:r,newValue:t(d,u,n)})):void 0!==n&&o(l,{name:r,newValue:n}));return l}},46260:function(t){"use strict";t.exports=function(t){var e="string"==typeof t?t.charCodeAt(0):t;return e>=97&&e<=122||e>=65&&e<=90}},7961:function(t,e,r){"use strict";var i=r(46260),n=r(46195);t.exports=function(t){return i(t)||n(t)}},48738:function(t){function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},46195:function(t){"use strict";t.exports=function(t){var e="string"==typeof t?t.charCodeAt(0):t;return e>=48&&e<=57}},79480:function(t){"use strict";t.exports=function(t){var e="string"==typeof t?t.charCodeAt(0):t;return e>=97&&e<=102||e>=65&&e<=70||e>=48&&e<=57}},82139:function(t){"use strict";t.exports=function(t){return r.test("number"==typeof t?e(t):t.charAt(0))};var e=String.fromCharCode,r=/\s/},93017:function(t){"use strict";t.exports=function(t){return r.test("number"==typeof t?e(t):t.charAt(0))};var e=String.fromCharCode,r=/\w/},92123:function(t){"use strict";t.exports=n;var e=["\\","`","*","{","}","[","]","(",")","#","+","-",".","!","_",">"],r=e.concat(["~","|"]),i=r.concat(["\n",'"',"$","%","&","'",",","/",":",";","<","=","?","@","^"]);function n(t){var n=t||{};return n.commonmark?i:n.gfm?r:e}n.default=e,n.gfm=r,n.commonmark=i},36522:function(t,e,r){var i=r(99294);t.exports=function(){return function(t){return i(t,"list",(function(t,e){var r,i,n=0;for(r=0,i=e.length;r<i;r++)"list"===e[r].type&&(n+=1);for(r=0,i=t.children.length;r<i;r++){var s=t.children[r];s.index=r,s.ordered=t.ordered}t.depth=n})),t}}},89435:function(t){"use strict";var e;t.exports=function(t){var r,i="&"+t+";";return(e=e||document.createElement("i")).innerHTML=i,(59!==(r=e.textContent).charCodeAt(r.length-1)||"semi"===t)&&(r!==i&&r)}},57574:function(t,e,r){"use strict";var i=r(37452),n=r(93580),s=r(46195),a=r(79480),o=r(7961),c=r(89435);t.exports=function(t,e){var r,s,a={};for(s in e||(e={}),p)r=e[s],a[s]=null==r?p[s]:r;return(a.position.indent||a.position.start)&&(a.indent=a.position.indent||[],a.position=a.position.start),function(t,e){var r,s,a,p,D,U,H,F,z,q,G,W,X,J,K,$,Q,Z,Y,tt,et=e.additional,rt=e.nonTerminated,it=e.text,nt=e.reference,st=e.warning,at=e.textContext,ot=e.referenceContext,ct=e.warningContext,ht=e.position,ut=e.indent||[],lt=t.length,pt=0,ft=-1,dt=ht.column||1,mt=ht.line||1,gt="",vt=[];for("string"==typeof et&&(et=et.charCodeAt(0)),$=xt(),F=st?function(t,e){var r=xt();r.column+=e,r.offset+=e,st.call(ct,B[t],r,t)}:l,pt--,lt++;++pt<lt;)if(D===d&&(dt=ut[ft]||1),(D=t.charCodeAt(pt))===v){if((H=t.charCodeAt(pt+1))===f||H===d||H===m||H===g||H===v||H===y||H!=H||et&&H===et){gt+=u(D),dt++;continue}for(W=X=pt+1,Y=X,H===k?(Y=++W,(H=t.charCodeAt(Y))===w||H===_?(J=E,Y=++W):J=S):J=C,r="",G="",p="",K=T[J],Y--;++Y<lt&&K(H=t.charCodeAt(Y));)p+=u(H),J===C&&h.call(i,p)&&(r=p,G=i[p]);(a=t.charCodeAt(Y)===x)&&(Y++,(s=J===C&&c(p))&&(r=p,G=s)),Z=1+Y-X,(a||rt)&&(p?J===C?(a&&!G?F(j,1):(r!==p&&(Z=1+(Y=W+r.length)-W,a=!1),a||(z=r?I:N,e.attribute?(H=t.charCodeAt(Y))===b?(F(z,Z),G=null):o(H)?G=null:F(z,Z):F(z,Z))),U=G):(a||F(O,Z),(tt=U=parseInt(p,P[J]))>=55296&&tt<=57343||tt>1114111?(F(R,Z),U=u(A)):U in n?(F(V,Z),U=n[U]):(q="",M(U)&&F(V,Z),U>65535&&(q+=u((U-=65536)>>>10|55296),U=56320|1023&U),U=q+u(U))):J!==C&&F(L,Z)),U?(yt(),$=xt(),pt=Y-1,dt+=Y-X+1,vt.push(U),(Q=xt()).offset++,nt&&nt.call(ot,U,{start:$,end:Q},t.slice(X-1,Y)),$=Q):(p=t.slice(X-1,Y),gt+=p,dt+=p.length,pt=Y-1)}else 10===D&&(mt++,ft++,dt=0),D==D?(gt+=u(D),dt++):yt();return vt.join("");function xt(){return{line:mt,column:dt,offset:pt+(ht.offset||0)}}function yt(){gt&&(vt.push(gt),it&&it.call(at,gt,{start:$,end:xt()}),gt="")}}(t,a)};var h={}.hasOwnProperty,u=String.fromCharCode,l=Function.prototype,p={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},f=9,d=10,m=12,g=32,v=38,x=59,y=60,b=61,k=35,w=88,_=120,A=65533,C="named",E="hexadecimal",S="decimal",P={};P[E]=16,P[S]=10;var T={};T[C]=o,T[S]=s,T[E]=a;var I=1,O=2,N=3,L=4,j=5,V=6,R=7,B={};function M(t){return t>=1&&t<=8||11===t||t>=13&&t<=31||t>=127&&t<=159||t>=64976&&t<=65007||65535==(65535&t)||65534==(65535&t)}B[I]="Named character references must be terminated by a semicolon",B[O]="Numeric character references must be terminated by a semicolon",B[N]="Named character references cannot be empty",B[L]="Numeric character references cannot be empty",B[j]="Named character references must be known",B[V]="Numeric character references cannot be disallowed",B[R]="Numeric character references cannot be outside the permissible Unicode range"},26470:function(t,e,r){"use strict";var i=r(34155);function n(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function s(t,e){for(var r,i="",n=0,s=-1,a=0,o=0;o<=t.length;++o){if(o<t.length)r=t.charCodeAt(o);else{if(47===r)break;r=47}if(47===r){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(i.length<2||2!==n||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){var c=i.lastIndexOf("/");if(c!==i.length-1){-1===c?(i="",n=0):n=(i=i.slice(0,c)).length-1-i.lastIndexOf("/"),s=o,a=0;continue}}else if(2===i.length||1===i.length){i="",n=0,s=o,a=0;continue}e&&(i.length>0?i+="/..":i="..",n=2)}else i.length>0?i+="/"+t.slice(s+1,o):i=t.slice(s+1,o),n=o-s-1;s=o,a=0}else 46===r&&-1!==a?++a:a=-1}return i}var a={resolve:function(){for(var t,e="",r=!1,a=arguments.length-1;a>=-1&&!r;a--){var o;a>=0?o=arguments[a]:(void 0===t&&(t=i.cwd()),o=t),n(o),0!==o.length&&(e=o+"/"+e,r=47===o.charCodeAt(0))}return e=s(e,!r),r?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(n(t),0===t.length)return".";var e=47===t.charCodeAt(0),r=47===t.charCodeAt(t.length-1);return 0!==(t=s(t,!e)).length||e||(t="."),t.length>0&&r&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return n(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var r=arguments[e];n(r),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":a.normalize(t)},relative:function(t,e){if(n(t),n(e),t===e)return"";if((t=a.resolve(t))===(e=a.resolve(e)))return"";for(var r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var i=t.length,s=i-r,o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var c=e.length-o,h=s<c?s:c,u=-1,l=0;l<=h;++l){if(l===h){if(c>h){if(47===e.charCodeAt(o+l))return e.slice(o+l+1);if(0===l)return e.slice(o+l)}else s>h&&(47===t.charCodeAt(r+l)?u=l:0===l&&(u=0));break}var p=t.charCodeAt(r+l);if(p!==e.charCodeAt(o+l))break;47===p&&(u=l)}var f="";for(l=r+u+1;l<=i;++l)l!==i&&47!==t.charCodeAt(l)||(0===f.length?f+="..":f+="/..");return f.length>0?f+e.slice(o+u):(o+=u,47===e.charCodeAt(o)&&++o,e.slice(o))},_makeLong:function(t){return t},dirname:function(t){if(n(t),0===t.length)return".";for(var e=t.charCodeAt(0),r=47===e,i=-1,s=!0,a=t.length-1;a>=1;--a)if(47===(e=t.charCodeAt(a))){if(!s){i=a;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":t.slice(0,i)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');n(t);var r,i=0,s=-1,a=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var o=e.length-1,c=-1;for(r=t.length-1;r>=0;--r){var h=t.charCodeAt(r);if(47===h){if(!a){i=r+1;break}}else-1===c&&(a=!1,c=r+1),o>=0&&(h===e.charCodeAt(o)?-1==--o&&(s=r):(o=-1,s=c))}return i===s?s=c:-1===s&&(s=t.length),t.slice(i,s)}for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!a){i=r+1;break}}else-1===s&&(a=!1,s=r+1);return-1===s?"":t.slice(i,s)},extname:function(t){n(t);for(var e=-1,r=0,i=-1,s=!0,a=0,o=t.length-1;o>=0;--o){var c=t.charCodeAt(o);if(47!==c)-1===i&&(s=!1,i=o+1),46===c?-1===e?e=o:1!==a&&(a=1):-1!==e&&(a=-1);else if(!s){r=o+1;break}}return-1===e||-1===i||0===a||1===a&&e===i-1&&e===r+1?"":t.slice(e,i)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return function(t,e){var r=e.dir||e.root,i=e.base||(e.name||"")+(e.ext||"");return r?r===e.root?r+i:r+"/"+i:i}(0,t)},parse:function(t){n(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var r,i=t.charCodeAt(0),s=47===i;s?(e.root="/",r=1):r=0;for(var a=-1,o=0,c=-1,h=!0,u=t.length-1,l=0;u>=r;--u)if(47!==(i=t.charCodeAt(u)))-1===c&&(h=!1,c=u+1),46===i?-1===a?a=u:1!==l&&(l=1):-1!==a&&(l=-1);else if(!h){o=u+1;break}return-1===a||-1===c||0===l||1===l&&a===c-1&&a===o+1?-1!==c&&(e.base=e.name=0===o&&s?t.slice(1,c):t.slice(o,c)):(0===o&&s?(e.name=t.slice(1,a),e.base=t.slice(1,c)):(e.name=t.slice(o,a),e.base=t.slice(o,c)),e.ext=t.slice(a,c)),o>0?e.dir=t.slice(0,o-1):s&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};a.posix=a,t.exports=a},12861:function(t,e,r){"use strict";var i=r(53278),n=r(47529),s=r(70970);function a(t){var e=i(s);e.prototype.options=n(e.prototype.options,this.data("settings"),t),this.Parser=e}t.exports=a,a.Parser=s},84870:function(t,e,r){"use strict";var i=r(47529),n=r(57574);t.exports=function(t){return s.raw=function(t,s,a){return n(t,i(a,{position:e(s),warning:r}))},s;function e(e){for(var r=t.offset,i=e.line,n=[];++i&&i in r;)n.push((r[i]||0)+1);return{start:e,indent:n}}function r(e,r,i){3!==i&&t.file.message(e,r)}function s(i,s,a){n(i,{position:e(s),warning:r,text:a,reference:a,textContext:t,referenceContext:t})}}},78515:function(t,e,r){"use strict";t.exports={position:!0,gfm:!0,commonmark:!1,footnotes:!1,pedantic:!1,blocks:r(94864)}},13047:function(t){"use strict";t.exports=function(t,e){for(var r=t.indexOf("\n",e);r>e&&" "===t.charAt(r-1);)r--;return r}},17993:function(t){"use strict";t.exports=function(t,e){return t.indexOf("`",e)}},80443:function(t){"use strict";t.exports=function(t,e){return t.indexOf("~~",e)}},16131:function(t){"use strict";t.exports=function(t,e){var r=t.indexOf("*",e),i=t.indexOf("_",e);return-1===i?r:-1===r||i<r?i:r}},83951:function(t){"use strict";t.exports=function(t,e){return t.indexOf("\\",e)}},55460:function(t){"use strict";t.exports=function(t,e){var r=t.indexOf("[",e),i=t.indexOf("![",e);return-1===i||r<i?r:i}},51594:function(t){"use strict";t.exports=function(t,e){var r=t.indexOf("**",e),i=t.indexOf("__",e);return-1===i?r:-1===r||i<r?i:r}},84329:function(t){"use strict";t.exports=function(t,e){return t.indexOf("<",e)}},91020:function(t){"use strict";t.exports=function(t,r){var i,n=e.length,s=-1,a=-1;if(!this.options.gfm)return-1;for(;++s<n;)-1!==(i=t.indexOf(e[s],r))&&(i<a||-1===a)&&(a=i);return a};var e=["https://","http://","mailto:"]},14604:function(t,e,r){"use strict";var i=r(47529),n=r(33183);t.exports=function(){var t,e=this,r=String(e.file),o={line:1,column:1,offset:0},c=i(o);return 65279===(r=r.replace(a,s)).charCodeAt(0)&&(r=r.slice(1),c.column++,c.offset++),t={type:"root",children:e.tokenizeBlock(r,c),position:{start:o,end:e.eof||i(o)}},e.options.position||n(t,!0),t};var s="\n",a=/\r\n|\r/g},70970:function(t,e,r){"use strict";var i=r(47529),n=r(78),s=r(14787),a=r(32816),o=r(84870),c=r(86691);function h(t,e){this.file=e,this.offset={},this.options=i(this.options),this.setOptions({}),this.inList=!1,this.inBlock=!1,this.inLink=!1,this.atStart=!0,this.toOffset=s(e).toOffset,this.unescape=a(this,"escape"),this.decode=o(this)}t.exports=h;var u=h.prototype;function l(t){var e,r=[];for(e in t)r.push(e);return r}u.setOptions=r(97870),u.parse=r(14604),u.options=r(78515),u.exitStart=n("atStart",!0),u.enterList=n("inList",!1),u.enterLink=n("inLink",!1),u.enterBlock=n("inBlock",!1),u.interruptParagraph=[["thematicBreak"],["atxHeading"],["fencedCode"],["blockquote"],["html"],["setextHeading",{commonmark:!1}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],u.interruptList=[["atxHeading",{pedantic:!1}],["fencedCode",{pedantic:!1}],["thematicBreak",{pedantic:!1}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],u.interruptBlockquote=[["indentedCode",{commonmark:!0}],["fencedCode",{commonmark:!0}],["atxHeading",{commonmark:!0}],["setextHeading",{commonmark:!0}],["thematicBreak",{commonmark:!0}],["html",{commonmark:!0}],["list",{commonmark:!0}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],u.blockTokenizers={newline:r(86648),indentedCode:r(31018),fencedCode:r(74967),blockquote:r(11153),atxHeading:r(62867),thematicBreak:r(69111),list:r(98332),setextHeading:r(96530),html:r(25215),footnote:r(18574),definition:r(10856),table:r(28029),paragraph:r(2431)},u.inlineTokenizers={escape:r(44833),autoLink:r(96507),url:r(65044),html:r(33943),link:r(38685),reference:r(47551),strong:r(51420),emphasis:r(78210),deletion:r(32967),code:r(89698),break:r(79310),text:r(44278)},u.blockMethods=l(u.blockTokenizers),u.inlineMethods=l(u.inlineTokenizers),u.tokenizeBlock=c("block"),u.tokenizeInline=c("inline"),u.tokenizeFactory=c},97870:function(t,e,r){"use strict";var i=r(47529),n=r(92123),s=r(78515);t.exports=function(t){var e,r,a=this,o=a.options;if(null==t)t={};else{if("object"!=typeof t)throw new Error("Invalid value `"+t+"` for setting `options`");t=i(t)}for(e in s){if(null==(r=t[e])&&(r=o[e]),"blocks"!==e&&"boolean"!=typeof r||"blocks"===e&&"object"!=typeof r)throw new Error("Invalid value `"+r+"` for setting `options."+e+"`");t[e]=r}return a.options=t,a.escape=n(t),a}},96507:function(t,e,r){"use strict";var i=r(82139),n=r(57574),s=r(84329);t.exports=p,p.locator=s,p.notInLink=!0;var a="<",o=">",c="@",h="/",u="mailto:",l=u.length;function p(t,e,r){var s,p,f,d,m,g,v,x,y,b,k,w;if(e.charAt(0)===a){for(s=this,p="",f=e.length,d=0,m="",v=!1,x="",d++,p=a;d<f&&(g=e.charAt(d),!(i(g)||g===o||g===c||":"===g&&e.charAt(d+1)===h));)m+=g,d++;if(m){if(x+=m,m="",x+=g=e.charAt(d),d++,g===c)v=!0;else{if(":"!==g||e.charAt(d+1)!==h)return;x+=h,d++}for(;d<f&&(g=e.charAt(d),!i(g)&&g!==o);)m+=g,d++;if(g=e.charAt(d),m&&g===o)return!!r||(b=x+=m,p+=x+g,(y=t.now()).column++,y.offset++,v&&(x.slice(0,l).toLowerCase()===u?(b=b.substr(l),y.column+=l,y.offset+=l):x=u+x),k=s.inlineTokenizers,s.inlineTokenizers={text:k.text},w=s.enterLink(),b=s.tokenizeInline(b,y),s.inlineTokenizers=k,w(),t(p)({type:"link",title:null,url:n(x,{nonTerminated:!1}),children:b}))}}}},11153:function(t,e,r){"use strict";var i=r(52745),n=r(76588);t.exports=function(t,e,r){for(var h,u,l,p,f,d,m,g,v,x=this,y=x.offset,b=x.blockTokenizers,k=x.interruptBlockquote,w=t.now(),_=w.line,A=e.length,C=[],E=[],S=[],P=0;P<A&&((u=e.charAt(P))===o||u===a);)P++;if(e.charAt(P)===c){if(r)return!0;for(P=0;P<A;){for(m=P,g=!1,-1===(p=e.indexOf(s,P))&&(p=A);P<A&&((u=e.charAt(P))===o||u===a);)P++;if(e.charAt(P)===c?(P++,g=!0,e.charAt(P)===o&&P++):P=m,f=e.slice(P,p),!g&&!i(f)){P=m;break}if(!g&&(l=e.slice(P),n(k,b,x,[t,l,!0])))break;d=m===P?f:e.slice(m,p),S.push(P-m),C.push(d),E.push(f),P=p+1}for(P=-1,A=S.length,h=t(C.join(s));++P<A;)y[_]=(y[_]||0)+S[P],_++;return v=x.enterBlock(),E=x.tokenizeBlock(E.join(s),w),v(),h({type:"blockquote",children:E})}};var s="\n",a="\t",o=" ",c=">"},79310:function(t,e,r){"use strict";var i=r(13047);t.exports=s,s.locator=i;var n=2;function s(t,e,r){for(var i,s=e.length,a=-1,o="";++a<s;){if("\n"===(i=e.charAt(a))){if(a<n)return;return!!r||t(o+=i)({type:"break"})}if(" "!==i)return;o+=i}}},74967:function(t,e,r){"use strict";var i=r(57257);t.exports=function(t,e,r){var l,p,f,d,m,g,v,x,y,b,k,w=this,_=w.options,A=e.length+1,C=0,E="";if(_.gfm){for(;C<A&&((f=e.charAt(C))===a||f===s);)E+=f,C++;if(b=C,(f=e.charAt(C))===o||f===c){for(C++,p=f,l=1,E+=f;C<A&&(f=e.charAt(C))===p;)E+=f,l++,C++;if(!(l<h)){for(;C<A&&((f=e.charAt(C))===a||f===s);)E+=f,C++;for(d="",m="";C<A&&(f=e.charAt(C))!==n&&f!==o&&f!==c;)f===a||f===s?m+=f:(d+=m+f,m=""),C++;if(!(f=e.charAt(C))||f===n){if(r)return!0;for((k=t.now()).column+=E.length,k.offset+=E.length,E+=d,d=w.decode.raw(w.unescape(d),k),m&&(E+=m),m="",x="",y="",g="",v="";C<A;)if(g+=x,v+=y,x="",y="",(f=e.charAt(C))===n){for(g?(x+=f,y+=f):E+=f,m="",C++;C<A&&(f=e.charAt(C))===a;)m+=f,C++;if(x+=m,y+=m.slice(b),!(m.length>=u)){for(m="";C<A&&(f=e.charAt(C))===p;)m+=f,C++;if(x+=m,y+=m,!(m.length<l)){for(m="";C<A&&((f=e.charAt(C))===a||f===s);)x+=f,y+=f,C++;if(!f||f===n)break}}}else g+=f,y+=f,C++;return t(E+=g+x)({type:"code",lang:d||null,value:i(v)})}}}}};var n="\n",s="\t",a=" ",o="~",c="`",h=3,u=4},31018:function(t,e,r){"use strict";var i=r(96464),n=r(57257);t.exports=function(t,e,r){for(var i,h,u,l=-1,p=e.length,f="",d="",m="",g="";++l<p;)if(i=e.charAt(l),u)if(u=!1,f+=m,d+=g,m="",g="",i===s)m=i,g=i;else for(f+=i,d+=i;++l<p;){if(!(i=e.charAt(l))||i===s){g=i,m=i;break}f+=i,d+=i}else if(i===o&&e.charAt(l+1)===i&&e.charAt(l+2)===i&&e.charAt(l+3)===i)m+=c,l+=3,u=!0;else if(i===a)m+=i,u=!0;else{for(h="";i===a||i===o;)h+=i,i=e.charAt(++l);if(i!==s)break;m+=h+i,g+=i}if(d)return!!r||t(f)({type:"code",lang:null,value:n(d)})};var s="\n",a="\t",o=" ",c=i(o,4)},89698:function(t,e,r){"use strict";var i=r(82139),n=r(17993);t.exports=a,a.locator=n;var s="`";function a(t,e,r){for(var n,a,o,c,h,u,l,p,f=e.length,d=0,m="",g="";d<f&&e.charAt(d)===s;)m+=s,d++;if(m){for(h=m,c=d,m="",p=e.charAt(d),o=0;d<f;){if(u=p,p=e.charAt(d+1),u===s?(o++,g+=u):(o=0,m+=u),o&&p!==s){if(o===c){h+=m+g,l=!0;break}m+=g,g=""}d++}if(!l){if(c%2!=0)return;m=""}if(r)return!0;for(n="",a="",f=m.length,d=-1;++d<f;)u=m.charAt(d),i(u)?a+=u:(a&&(n&&(n+=a),a=""),n+=u);return t(h)({type:"inlineCode",value:n})}}},10856:function(t,e,r){"use strict";var i=r(82139),n=r(24405);t.exports=x,x.notInList=!0,x.notInBlock=!0;var s='"',a="'",o="\\",c="\n",h="\t",u=" ",l="[",p="]",f="(",d=")",m=":",g="<",v=">";function x(t,e,r){for(var i,v,x,k,w,_,A,C,E=this,S=E.options.commonmark,P=0,T=e.length,I="";P<T&&((k=e.charAt(P))===u||k===h);)I+=k,P++;if((k=e.charAt(P))===l){for(P++,I+=k,x="";P<T&&(k=e.charAt(P))!==p;)k===o&&(x+=k,P++,k=e.charAt(P)),x+=k,P++;if(x&&e.charAt(P)===p&&e.charAt(P+1)===m){for(_=x,P=(I+=x+p+m).length,x="";P<T&&((k=e.charAt(P))===h||k===u||k===c);)I+=k,P++;if(x="",i=I,(k=e.charAt(P))===g){for(P++;P<T&&y(k=e.charAt(P));)x+=k,P++;if((k=e.charAt(P))===y.delimiter)I+=g+x+k,P++;else{if(S)return;P-=x.length+1,x=""}}if(!x){for(;P<T&&b(k=e.charAt(P));)x+=k,P++;I+=x}if(x){for(A=x,x="";P<T&&((k=e.charAt(P))===h||k===u||k===c);)x+=k,P++;if(w=null,(k=e.charAt(P))===s?w=s:k===a?w=a:k===f&&(w=d),w){if(!x)return;for(P=(I+=x+k).length,x="";P<T&&(k=e.charAt(P))!==w;){if(k===c){if(P++,(k=e.charAt(P))===c||k===w)return;x+=c}x+=k,P++}if((k=e.charAt(P))!==w)return;v=I,I+=x+k,P++,C=x,x=""}else x="",P=I.length;for(;P<T&&((k=e.charAt(P))===h||k===u);)I+=k,P++;return(k=e.charAt(P))&&k!==c?void 0:!!r||(i=t(i).test().end,A=E.decode.raw(E.unescape(A),i,{nonTerminated:!1}),C&&(v=t(v).test().end,C=E.decode.raw(E.unescape(C),v)),t(I)({type:"definition",identifier:n(_),title:C||null,url:A}))}}}}function y(t){return t!==v&&t!==l&&t!==p}function b(t){return t!==l&&t!==p&&!i(t)}y.delimiter=v},32967:function(t,e,r){"use strict";var i=r(82139),n=r(80443);t.exports=o,o.locator=n;var s="~",a="~~";function o(t,e,r){var n,o,c,h="",u="",l="",p="";if(this.options.gfm&&e.charAt(0)===s&&e.charAt(1)===s&&!i(e.charAt(2)))for(n=1,o=e.length,(c=t.now()).column+=2,c.offset+=2;++n<o;){if(!((h=e.charAt(n))!==s||u!==s||l&&i(l)))return!!r||t(a+p+a)({type:"delete",children:this.tokenizeInline(p,c)});p+=u,l=u,u=h}}},78210:function(t,e,r){"use strict";var i=r(52745),n=r(93017),s=r(82139),a=r(16131);t.exports=h,h.locator=a;var o="*",c="_";function h(t,e,r){var a,h,u,l,p,f,d,m=0,g=e.charAt(m);if(!(g!==o&&g!==c||(h=this.options.pedantic,p=g,u=g,f=e.length,m++,l="",g="",h&&s(e.charAt(m)))))for(;m<f;){if(d=g,!((g=e.charAt(m))!==u||h&&s(d))){if((g=e.charAt(++m))!==u){if(!i(l)||d===u)return;if(!h&&u===c&&n(g)){l+=u;continue}return!!r||((a=t.now()).column++,a.offset++,t(p+l+u)({type:"emphasis",children:this.tokenizeInline(l,a)}))}l+=u}h||"\\"!==g||(l+=g,g=e.charAt(++m)),l+=g,m++}}},44833:function(t,e,r){"use strict";var i=r(83951);function n(t,e,r){var i,n;if("\\"===e.charAt(0)&&(i=e.charAt(1),-1!==this.escape.indexOf(i)))return!!r||(n="\n"===i?{type:"break"}:{type:"text",value:i},t("\\"+i)(n))}t.exports=n,n.locator=i},18574:function(t,e,r){"use strict";var i=r(82139),n=r(24405);t.exports=d,d.notInList=!0,d.notInBlock=!0;var s="\\",a="\n",o="\t",c=" ",h="[",u="]",l="^",p=":",f=/^( {4}|\t)?/gm;function d(t,e,r){var d,m,g,v,x,y,b,k,w,_,A,C,E=this,S=E.offset;if(E.options.footnotes){for(d=0,m=e.length,g="",v=t.now(),x=v.line;d<m&&(w=e.charAt(d),i(w));)g+=w,d++;if(e.charAt(d)===h&&e.charAt(d+1)===l){for(d=(g+=h+l).length,b="";d<m&&(w=e.charAt(d))!==u;)w===s&&(b+=w,d++,w=e.charAt(d)),b+=w,d++;if(b&&e.charAt(d)===u&&e.charAt(d+1)===p){if(r)return!0;for(_=n(b),d=(g+=b+u+p).length;d<m&&((w=e.charAt(d))===o||w===c);)g+=w,d++;for(v.column+=g.length,v.offset+=g.length,b="",y="",k="";d<m;){if((w=e.charAt(d))===a){for(k=w,d++;d<m&&(w=e.charAt(d))===a;)k+=w,d++;for(b+=k,k="";d<m&&(w=e.charAt(d))===c;)k+=w,d++;if(0===k.length)break;b+=k}b&&(y+=b,b=""),y+=w,d++}return g+=y,y=y.replace(f,(function(t){return S[x]=(S[x]||0)+t.length,x++,""})),A=t(g),C=E.enterBlock(),y=E.tokenizeBlock(y,v),C(),A({type:"footnoteDefinition",identifier:_,children:y})}}}}},62867:function(t){"use strict";t.exports=function(t,a,o){for(var c,h,u,l=this.options,p=a.length+1,f=-1,d=t.now(),m="",g="";++f<p;){if((c=a.charAt(f))!==i&&c!==r){f--;break}m+=c}for(u=0;++f<=p;){if((c=a.charAt(f))!==n){f--;break}m+=c,u++}if(!(u>s)&&u&&(l.pedantic||a.charAt(f+1)!==n)){for(p=a.length+1,h="";++f<p;){if((c=a.charAt(f))!==i&&c!==r){f--;break}h+=c}if(l.pedantic||0!==h.length||!c||c===e){if(o)return!0;for(m+=h,h="",g="";++f<p&&(c=a.charAt(f))&&c!==e;)if(c===i||c===r||c===n){for(;c===i||c===r;)h+=c,c=a.charAt(++f);for(;c===n;)h+=c,c=a.charAt(++f);for(;c===i||c===r;)h+=c,c=a.charAt(++f);f--}else g+=h+c,h="";return d.column+=m.length,d.offset+=m.length,t(m+=g+h)({type:"heading",depth:u,children:this.tokenizeInline(g,d)})}}};var e="\n",r="\t",i=" ",n="#",s=6},96530:function(t){"use strict";t.exports=function(t,a,o){for(var c,h,u,l,p,f=t.now(),d=a.length,m=-1,g="";++m<d;){if((u=a.charAt(m))!==i||m>=n){m--;break}g+=u}for(c="",h="";++m<d;){if((u=a.charAt(m))===e){m--;break}u===i||u===r?h+=u:(c+=h+u,h="")}if(f.column+=g.length,f.offset+=g.length,g+=c+h,u=a.charAt(++m),l=a.charAt(++m),u===e&&s[l]){for(g+=u,h=l,p=s[l];++m<d;){if((u=a.charAt(m))!==l){if(u!==e)return;m--;break}h+=u}return!!o||t(g+h)({type:"heading",depth:p,children:this.tokenizeInline(c,f)})}};var e="\n",r="\t",i=" ",n=3,s={"=":1,"-":2}},25215:function(t,e,r){"use strict";var i=r(24706).g;t.exports=function(t,e,r){for(var c,h,u,l,p,f,d,m=this.options.blocks,g=e.length,v=0,x=[[/^<(script|pre|style)(?=(\s|>|$))/i,/<\/(script|pre|style)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Za-z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+m.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(i.source+"\\s*$"),/^$/,!1]];v<g&&((l=e.charAt(v))===n||l===s);)v++;if(e.charAt(v)===o){for(c=-1===(c=e.indexOf(a,v+1))?g:c,h=e.slice(v,c),u=-1,p=x.length;++u<p;)if(x[u][0].test(h)){f=x[u];break}if(f){if(r)return f[2];if(v=c,!f[1].test(h))for(;v<g;){if(c=-1===(c=e.indexOf(a,v+1))?g:c,h=e.slice(v+1,c),f[1].test(h)){h&&(v=c);break}v=c}return t(d=e.slice(0,v))({type:"html",value:d})}}};var n="\t",s=" ",a="\n",o="<"},33943:function(t,e,r){"use strict";var i=r(46260),n=r(84329),s=r(24706)._;t.exports=c,c.locator=n;var a=/^<a /i,o=/^<\/a>/i;function c(t,e,r){var n,c,h=this,u=e.length;if(!("<"!==e.charAt(0)||u<3)&&(n=e.charAt(1),(i(n)||"?"===n||"!"===n||"/"===n)&&(c=e.match(s))))return!!r||(c=c[0],!h.inLink&&a.test(c)?h.inLink=!0:h.inLink&&o.test(c)&&(h.inLink=!1),t(c)({type:"html",value:c}))}},38685:function(t,e,r){"use strict";var i=r(82139),n=r(55460);t.exports=x,x.locator=n;var s={}.hasOwnProperty,a="\\",o="[",c="]",h="(",u=")",l="<",p=">",f="`",d='"',m="'",g={};g[d]=d,g[m]=m;var v={};function x(t,e,r){var n,d,m,x,y,b,k,w,_,A,C,E,S,P,T,I,O,N,L,j=this,V="",R=0,B=e.charAt(0),M=j.options.pedantic,D=j.options.commonmark,U=j.options.gfm;if("!"===B&&(_=!0,V=B,B=e.charAt(++R)),B===o&&(_||!j.inLink)){for(V+=B,T="",R++,E=e.length,P=0,(O=t.now()).column+=R,O.offset+=R;R<E;){if(b=B=e.charAt(R),B===f){for(d=1;e.charAt(R+1)===f;)b+=B,R++,d++;m?d>=m&&(m=0):m=d}else if(B===a)R++,b+=e.charAt(R);else if(m&&!U||B!==o){if((!m||U)&&B===c){if(!P){if(!M)for(;R<E&&(B=e.charAt(R+1),i(B));)b+=B,R++;if(e.charAt(R+1)!==h)return;b+=h,n=!0,R++;break}P--}}else P++;T+=b,b="",R++}if(n){for(A=T,V+=T+b,R++;R<E&&(B=e.charAt(R),i(B));)V+=B,R++;if(B=e.charAt(R),w=D?v:g,T="",x=V,B===l){for(R++,x+=l;R<E&&(B=e.charAt(R))!==p;){if(D&&"\n"===B)return;T+=B,R++}if(e.charAt(R)!==p)return;V+=l+T+p,I=T,R++}else{for(B=null,b="";R<E&&(B=e.charAt(R),!b||!s.call(w,B));){if(i(B)){if(!M)break;b+=B}else{if(B===h)P++;else if(B===u){if(0===P)break;P--}T+=b,b="",B===a&&(T+=a,B=e.charAt(++R)),T+=B}R++}I=T,R=(V+=T).length}for(T="";R<E&&(B=e.charAt(R),i(B));)T+=B,R++;if(B=e.charAt(R),V+=T,T&&s.call(w,B))if(R++,V+=B,T="",C=w[B],y=V,D){for(;R<E&&(B=e.charAt(R))!==C;)B===a&&(T+=a,B=e.charAt(++R)),R++,T+=B;if((B=e.charAt(R))!==C)return;for(S=T,V+=T+B,R++;R<E&&(B=e.charAt(R),i(B));)V+=B,R++}else for(b="";R<E;){if((B=e.charAt(R))===C)k&&(T+=C+b,b=""),k=!0;else if(k){if(B===u){V+=T+C+b,S=T;break}i(B)?b+=B:(T+=C+b+B,b="",k=!1)}else T+=B;R++}if(e.charAt(R)===u)return!!r||(V+=u,I=j.decode.raw(j.unescape(I),t(x).test().end,{nonTerminated:!1}),S&&(y=t(y).test().end,S=j.decode.raw(j.unescape(S),y)),L={type:_?"image":"link",title:S||null,url:I},_?L.alt=j.decode.raw(j.unescape(A),O)||null:(N=j.enterLink(),L.children=j.tokenizeInline(A,O),N()),t(V)(L))}}}v[d]=d,v[m]=m,v[h]=u},98332:function(t,e,r){"use strict";var i=r(52745),n=r(96464),s=r(46195),a=r(22299),o=r(96514),c=r(76588);t.exports=function(t,e,r){for(var n,a,o,m,v,x,y,b,k,E,S,P,T,I,O,N,L,j,V,R,B,M,D,U,H=this,F=H.options.commonmark,z=H.options.pedantic,q=H.blockTokenizers,G=H.interruptList,W=0,X=e.length,J=null,K=0;W<X;){if((m=e.charAt(W))===d)K+=g-K%g;else{if(m!==p)break;K++}W++}if(!(K>=g)){if(m=e.charAt(W),n=F?A:_,!0===w[m])v=m,o=!1;else{for(o=!0,a="";W<X&&(m=e.charAt(W),s(m));)a+=m,W++;if(m=e.charAt(W),!a||!0!==n[m])return;J=parseInt(a,10),v=m}if((m=e.charAt(++W))===p||m===d){if(r)return!0;for(W=0,I=[],O=[],N=[];W<X;){for(y=W,b=!1,U=!1,-1===(x=e.indexOf(f,W))&&(x=X),D=W+g,K=0;W<X;){if((m=e.charAt(W))===d)K+=g-K%g;else{if(m!==p)break;K++}W++}if(K>=g&&(U=!0),L&&K>=L.indent&&(U=!0),m=e.charAt(W),k=null,!U){if(!0===w[m])k=m,W++,K++;else{for(a="";W<X&&(m=e.charAt(W),s(m));)a+=m,W++;m=e.charAt(W),W++,a&&!0===n[m]&&(k=m,K+=a.length+1)}if(k)if((m=e.charAt(W))===d)K+=g-K%g,W++;else if(m===p){for(D=W+g;W<D&&e.charAt(W)===p;)W++,K++;W===D&&e.charAt(W)===p&&(W-=g-1,K-=g-1)}else m!==f&&""!==m&&(k=null)}if(k){if(!z&&v!==k)break;b=!0}else F||U||e.charAt(y)!==p?F&&L&&(U=K>=L.indent||K>g):U=!0,b=!1,W=y;if(S=e.slice(y,x),E=y===W?S:e.slice(W,x),(k===h||k===u||k===l)&&q.thematicBreak.call(H,t,S,!0))break;if(P=T,T=!i(E).length,U&&L)L.value=L.value.concat(N,S),O=O.concat(N,S),N=[];else if(b)0!==N.length&&(L.value.push(""),L.trail=N.concat()),L={value:[S],indent:K,trail:[]},I.push(L),O=O.concat(N,S),N=[];else if(T){if(P)break;N.push(S)}else{if(P)break;if(c(G,q,H,[t,S,!0]))break;L.value=L.value.concat(N,S),O=O.concat(N,S),N=[]}W=x+1}for(B=t(O.join(f)).reset({type:"list",ordered:o,start:J,loose:null,children:[]}),j=H.enterList(),V=H.enterBlock(),R=!1,W=-1,X=I.length;++W<X;)L=I[W].value.join(f),M=t.now(),(L=t(L)(C(H,L,M),B)).loose&&(R=!0),L=I[W].trail.join(f),W!==X-1&&(L+=f),t(L);return j(),V(),B.loose=R,B}}};var h="*",u="_",l="-",p=" ",f="\n",d="\t",m="x",g=4,v=/\n\n(?!\s*$)/,x=/^\[([ \t]|x|X)][ \t]/,y=/^([ \t]*)([*+-]|\d+[.)])( {1,4}(?! )| |\t|$|(?=\n))([^\n]*)/,b=/^([ \t]*)([*+-]|\d+[.)])([ \t]+)/,k=/^( {1,4}|\t)?/gm,w={};w[h]=!0,w["+"]=!0,w[l]=!0;var _={".":!0},A={};function C(t,e,r){var i,n,s=t.offset,a=null;return e=(t.options.pedantic?E:S).apply(null,arguments),t.options.gfm&&(i=e.match(x))&&(n=i[0].length,a=i[1].toLowerCase()===m,s[r.line]+=n,e=e.slice(n)),{type:"listItem",loose:v.test(e)||e.charAt(e.length-1)===f,checked:a,children:t.tokenizeBlock(e,r)}}function E(t,e,r){var i=t.offset,n=r.line;return e=e.replace(b,s),n=r.line,e.replace(k,s);function s(t){return i[n]=(i[n]||0)+t.length,n++,""}}function S(t,e,r){var i,s,c,h,u,l,d,m=t.offset,g=r.line;for(h=(e=e.replace(y,(function(t,e,r,a,o){return s=e+r+a,c=o,Number(r)<10&&s.length%2==1&&(r=p+r),(i=e+n(p,r.length)+a)+c}))).split(f),(u=o(e,a(i).indent).split(f))[0]=c,m[g]=(m[g]||0)+s.length,g++,l=0,d=h.length;++l<d;)m[g]=(m[g]||0)+h[l].length-u[l].length,g++;return u.join(f)}A["."]=!0,A[")"]=!0},86648:function(t,e,r){"use strict";var i=r(82139);t.exports=function(t,e,r){var n,s,a,o,c=e.charAt(0);if("\n"===c){if(r)return!0;for(o=1,n=e.length,s=c,a="";o<n&&(c=e.charAt(o),i(c));)a+=c,"\n"===c&&(s+=a,a=""),o++;t(s)}}},2431:function(t,e,r){"use strict";var i=r(52745),n=r(46195),s=r(57257),a=r(76588);t.exports=function(t,e,r){for(var l,p,f,d,m,g=this,v=g.options,x=v.commonmark,y=v.gfm,b=g.blockTokenizers,k=g.interruptParagraph,w=e.indexOf(o),_=e.length;w<_;){if(-1===w){w=_;break}if(e.charAt(w+1)===o)break;if(x){for(d=0,l=w+1;l<_;){if((f=e.charAt(l))===c){d=u;break}if(f!==h)break;d++,l++}if(d>=u){w=e.indexOf(o,w+1);continue}}if(p=e.slice(w+1),a(k,b,g,[t,p,!0]))break;if(b.list.call(g,t,p,!0)&&(g.inList||x||y&&!n(i.left(p).charAt(0))))break;if(l=w,-1!==(w=e.indexOf(o,w+1))&&""===i(e.slice(l,w))){w=l;break}}return p=e.slice(0,w),""===i(p)?(t(p),null):!!r||(m=t.now(),t(p=s(p))({type:"paragraph",children:g.tokenizeInline(p,m)}))};var o="\n",c="\t",h=" ",u=4},47551:function(t,e,r){"use strict";var i=r(82139),n=r(55460),s=r(24405);t.exports=g,g.locator=n;var a="link",o="image",c="footnote",h="shortcut",u="collapsed",l="full",p="^",f="\\",d="[",m="]";function g(t,e,r){var n,g,v,x,y,b,k,w,_=this,A=e.charAt(0),C=0,E=e.length,S="",P="",T=a,I=h;if("!"===A&&(T=o,P=A,A=e.charAt(++C)),A===d){if(C++,P+=A,b="",_.options.footnotes&&e.charAt(C)===p){if(T===o)return;P+=p,C++,T=c}for(w=0;C<E;){if((A=e.charAt(C))===d)k=!0,w++;else if(A===m){if(!w)break;w--}A===f&&(b+=f,A=e.charAt(++C)),b+=A,C++}if(S=b,n=b,(A=e.charAt(C))===m){for(C++,S+=A,b="";C<E&&(A=e.charAt(C),i(A));)b+=A,C++;if(A=e.charAt(C),T!==c&&A===d){for(g="",b+=A,C++;C<E&&(A=e.charAt(C))!==d&&A!==m;)A===f&&(g+=f,A=e.charAt(++C)),g+=A,C++;(A=e.charAt(C))===m?(I=g?l:u,b+=g+A,C++):g="",S+=b,b=""}else{if(!n)return;g=n}if(I===l||!k)return S=P+S,T===a&&_.inLink?null:!!r||(T===c&&-1!==n.indexOf(" ")?t(S)({type:"footnote",children:this.tokenizeInline(n,t.now())}):((v=t.now()).column+=P.length,v.offset+=P.length,x={type:T+"Reference",identifier:s(g=I===l?g:n)},T!==a&&T!==o||(x.referenceType=I),T===a?(y=_.enterLink(),x.children=_.tokenizeInline(n,v),y()):T===o&&(x.alt=_.decode.raw(_.unescape(n),v)||null),t(S)(x)))}}}},51420:function(t,e,r){"use strict";var i=r(52745),n=r(82139),s=r(51594);t.exports=c,c.locator=s;var a="*",o="_";function c(t,e,r){var s,c,h,u,l,p,f,d=0,m=e.charAt(d);if(!(m!==a&&m!==o||e.charAt(++d)!==m||(c=this.options.pedantic,l=(h=m)+h,p=e.length,d++,u="",m="",c&&n(e.charAt(d)))))for(;d<p;){if(f=m,!((m=e.charAt(d))!==h||e.charAt(d+1)!==h||c&&n(f))&&(m=e.charAt(d+2))!==h){if(!i(u))return;return!!r||((s=t.now()).column+=2,s.offset+=2,t(l+u+l)({type:"strong",children:this.tokenizeInline(u,s)}))}c||"\\"!==m||(u+=m,m=e.charAt(++d)),u+=m,d++}}},28029:function(t,e,r){"use strict";var i=r(82139);t.exports=function(t,e,r){var x,y,b,k,w,_,A,C,E,S,P,T,I,O,N,L,j,V,R,B,M,D,U,H;if(this.options.gfm){for(x=0,V=0,_=e.length+1,A=[];x<_;){if(D=e.indexOf(u,x),U=e.indexOf(o,x+1),-1===D&&(D=e.length),-1===U||U>D){if(V<f)return;break}A.push(e.slice(x,D)),V++,x=D+1}for(k=A.join(u),x=0,_=(y=A.splice(1,1)[0]||[]).length,V--,b=!1,P=[];x<_;){if((E=y.charAt(x))===o){if(S=null,!1===b){if(!1===H)return}else P.push(b),b=!1;H=!1}else if(E===a)S=!0,b=b||v;else if(E===c)b=b===d?m:S&&b===v?g:d;else if(!i(E))return;x++}if(!1!==b&&P.push(b),!(P.length<p)){if(r)return!0;for(j=-1,B=[],M=t(k).reset({type:"table",align:P,children:B});++j<V;){for(R=A[j],w={type:"tableRow",children:[]},j&&t(u),t(R).reset(w,M),_=R.length+1,x=0,C="",T="",I=!0,O=null,N=null;x<_;)if((E=R.charAt(x))!==l&&E!==h){if(""===E||E===o)if(I)t(E);else{if(E&&N){C+=E,x++;continue}!T&&!E||I||(k=T,C.length>1&&(E?(k+=C.slice(0,C.length-1),C=C.charAt(C.length-1)):(k+=C,C="")),L=t.now(),t(k)({type:"tableCell",children:this.tokenizeInline(T,L)},w)),t(C+E),C="",T=""}else if(C&&(T+=C,C=""),T+=E,E===n&&x!==_-2&&(T+=R.charAt(x+1),x++),E===s){for(O=1;R.charAt(x+1)===E;)T+=E,x++,O++;N?O>=N&&(N=0):N=O}I=!1,x++}else T?C+=E:t(E),x++;j||t(u+y)}return M}}};var n="\\",s="`",a="-",o="|",c=":",h=" ",u="\n",l="\t",p=1,f=2,d="left",m="center",g="right",v=null},44278:function(t){"use strict";t.exports=function(t,e,r){var i,n,s,a,o,c,h,u,l,p,f=this;if(r)return!0;for(a=(i=f.inlineMethods).length,n=f.inlineTokenizers,s=-1,l=e.length;++s<a;)"text"!==(u=i[s])&&n[u]&&((h=n[u].locator)||t.file.fail("Missing locator: `"+u+"`"),-1!==(c=h.call(f,e,1))&&c<l&&(l=c));o=e.slice(0,l),p=t.now(),f.decode(o,p,(function(e,r,i){t(i||e)({type:"text",value:e})}))}},69111:function(t){"use strict";t.exports=function(t,c,h){for(var u,l,p,f,d=-1,m=c.length+1,g="";++d<m&&((u=c.charAt(d))===r||u===i);)g+=u;if(u===n||u===a||u===s)for(l=u,g+=u,p=1,f="";++d<m;)if((u=c.charAt(d))===l)p++,g+=f+l,f="";else{if(u!==i)return p>=o&&(!u||u===e)?(g+=f,!!h||t(g)({type:"thematicBreak"})):void 0;f+=u}};var e="\n",r="\t",i=" ",n="*",s="_",a="-",o=3},65044:function(t,e,r){"use strict";var i=r(57574),n=r(82139),s=r(91020);t.exports=m,m.locator=s,m.notInLink=!0;var a="[",o="]",c="(",h=")",u="<",l="@",p="mailto:",f=["http://","https://",p],d=f.length;function m(t,e,r){var s,m,g,v,x,y,b,k,w,_,A,C,E=this;if(E.options.gfm){for(s="",v=-1,k=d;++v<k;)if(y=f[v],(b=e.slice(0,y.length)).toLowerCase()===y){s=b;break}if(s){for(v=s.length,k=e.length,w="",_=0;v<k&&(g=e.charAt(v),!n(g)&&g!==u)&&("."!==g&&","!==g&&":"!==g&&";"!==g&&'"'!==g&&"'"!==g&&")"!==g&&"]"!==g||(A=e.charAt(v+1))&&!n(A))&&(g!==c&&g!==a||_++,g!==h&&g!==o||!(--_<0));)w+=g,v++;if(w){if(m=s+=w,y===p){if(-1===(x=w.indexOf(l))||x===k-1)return;m=m.substr(p.length)}return!!r||(C=E.enterLink(),m=E.tokenizeInline(m,t.now()),C(),t(s)({type:"link",title:null,url:i(s,{nonTerminated:!1}),children:m}))}}}}},86691:function(t){"use strict";t.exports=function(t){return function(i,n){var s,a,o,c,h,u=this,l=u.offset,p=[],f=u[t+"Methods"],d=u[t+"Tokenizers"],m=n.line,g=n.column;if(!i)return p;for(b.now=x,b.file=u.file,v("");i;){for(s=-1,a=f.length,c=!1;++s<a&&(!(o=d[f[s]])||o.onlyAtStart&&!u.atStart||o.notInList&&u.inList||o.notInBlock&&u.inBlock||o.notInLink&&u.inLink||(h=i.length,o.apply(u,[b,i]),!(c=h!==i.length))););c||u.file.fail(new Error("Infinite loop"),b.now())}return u.eof=x(),p;function v(t){for(var e=-1,r=t.indexOf("\n");-1!==r;)m++,e=r,r=t.indexOf("\n",r+1);-1===e?g+=t.length:g=t.length-e,m in l&&(-1!==e?g+=l[m]:g<=l[m]&&(g=l[m]+1))}function x(){var t={line:m,column:g};return t.offset=u.toOffset(t),t}function y(t){this.start=t,this.end=x()}function b(t){var n,s=function(){var t=[],e=m+1;return function(){for(var r=m+1;e<r;)t.push((l[e]||0)+1),e++;return t}}(),a=(n=x(),function(t,e){var r=t.position,i=r?r.start:n,s=[],a=r&&r.end.line,o=n.line;if(t.position=new y(i),r&&e&&r.indent){if(s=r.indent,a<o){for(;++a<o;)s.push((l[a]||0)+1);s.push(n.column)}e=s.concat(e)}return t.position.indent=e||[],t}),o=x();return function(t){i.substring(0,t.length)!==t&&u.file.fail(new Error("Incorrectly eaten value: please report this warning on http://git.io/vg5Ft"),x())}(t),c.reset=h,h.test=f,c.test=f,i=i.substring(t.length),v(t),s=s(),c;function c(t,i){return a(function(t,i){var n=i?i.children:p,s=n[n.length-1];return s&&t.type===s.type&&t.type in e&&r(s)&&r(t)&&(t=e[t.type].call(u,s,t)),t!==s&&n.push(t),u.atStart&&0!==p.length&&u.exitStart(),t}(a(t),i),s)}function h(){var e=c.apply(null,arguments);return m=o.line,g=o.column,i=t+i,e}function f(){var e=a({});return m=o.line,g=o.column,i=t+i,e.position}}}};var e={text:function(t,e){return t.value+=e.value,t},blockquote:function(t,e){return this.options.commonmark?e:(t.children=t.children.concat(e.children),t)}};function r(t){var e,r;return"text"!==t.type||!t.position||(e=t.position.start,r=t.position.end,e.line!==r.line||r.column-e.column===t.value.length)}},32816:function(t){"use strict";t.exports=function(t,e){return function(r){for(var i,n=0,s=r.indexOf("\\"),a=t[e],o=[];-1!==s;)o.push(r.slice(n,s)),n=s+1,(i=r.charAt(n))&&-1!==a.indexOf(i)||o.push("\\"),s=r.indexOf("\\",n);return o.push(r.slice(n)),o.join("")}}},22299:function(t){"use strict";t.exports=function(t){for(var r,i=0,n=0,s=t.charAt(i),a={};s in e;)n+=r=e[s],r>1&&(n=Math.floor(n/r)*r),a[n]=i,s=t.charAt(++i);return{indent:n,stops:a}};var e={" ":1,"\t":4}},24706:function(t,e){"use strict";var r="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\u0000-\\u0020]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",i="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>";e.g=new RegExp("^(?:"+r+"|"+i+")"),e._=new RegExp("^(?:"+r+"|"+i+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?].*?[?]>|<![A-Za-z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)")},76588:function(t){"use strict";t.exports=function(t,e,r,i){for(var n,s,a,o,c,h,u=["pedantic","commonmark"],l=u.length,p=t.length,f=-1;++f<p;){for(s=(n=t[f])[1]||{},a=n[0],o=-1,h=!1;++o<l;)if(void 0!==s[c=u[o]]&&s[c]!==r.options[c]){h=!0;break}if(!h&&e[a].apply(r,i))return!0}return!1}},24405:function(t,e,r){"use strict";var i=r(69514);t.exports=function(t){return i(t).toLowerCase()}},96514:function(t,e,r){"use strict";var i=r(52745),n=r(96464),s=r(22299);t.exports=function(t,e){var r,h,u,l,p=t.split(o),f=p.length+1,d=1/0,m=[];for(p.unshift(n(a,e)+"!");f--;)if(h=s(p[f]),m[f]=h.stops,0!==i(p[f]).length){if(!h.indent){d=1/0;break}h.indent>0&&h.indent<d&&(d=h.indent)}if(d!==1/0)for(f=p.length;f--;){for(u=m[f],r=d;r&&!(r in u);)r--;l=0!==i(p[f]).length&&d&&r!==d?c:"",p[f]=l+p[f].slice(r in u?u[r]+1:0)}return p.shift(),p.join(o)};var a=" ",o="\n",c="\t"},96464:function(t){"use strict";var e,r="";t.exports=function(t,i){if("string"!=typeof t)throw new TypeError("expected a string");if(1===i)return t;if(2===i)return t+t;var n=t.length*i;if(e!==t||void 0===e)e=t,r="";else if(r.length>=n)return r.substr(0,n);for(;n>r.length&&i>1;)1&i&&(r+=t),i>>=1,t+=t;return r=(r+=t).substr(0,n)}},38245:function(t,e,r){"use strict";var i=r(26470);t.exports=function(t,e){if("string"!=typeof t)return t;if(0===t.length)return t;var r=i.basename(t,i.extname(t))+e;return i.join(i.dirname(t),r)}},78:function(t){"use strict";t.exports=function(t,e,r){return function(){var i=r||this,n=i[t];return i[t]=!e,function(){i[t]=n}}}},57257:function(t){"use strict";t.exports=function(t){return String(t).replace(/\n+$/,"")}},52745:function(t,e){(e=t.exports=function(t){return t.replace(/^\s*|\s*$/g,"")}).left=function(t){return t.replace(/^\s*/,"")},e.right=function(t){return t.replace(/\s*$/,"")}},28281:function(t,e,r){"use strict";var i=r(43368);t.exports=s,s.wrap=i;var n=[].slice;function s(){var t=[],e={run:function(){var e=-1,r=n.call(arguments,0,-1),s=arguments[arguments.length-1];if("function"!=typeof s)throw new Error("Expected function as last argument, not "+s);(function a(o){var c=t[++e],h=n.call(arguments,0).slice(1),u=r.length,l=-1;if(o)s(o);else{for(;++l<u;)null!==h[l]&&void 0!==h[l]||(h[l]=r[l]);r=h,c?i(c,a).apply(null,r):s.apply(null,[null].concat(r))}}).apply(null,[null].concat(r))},use:function(r){if("function"!=typeof r)throw new Error("Expected `fn` to be a function, not "+r);return t.push(r),e}};return e}},43368:function(t){"use strict";var e=[].slice;t.exports=function(t,r){var i;return function(){var r,a=e.call(arguments,0),o=t.length>a.length;o&&a.push(n);try{r=t.apply(null,a)}catch(t){if(o&&i)throw t;return n(t)}o||(r&&"function"==typeof r.then?r.then(s,n):r instanceof Error?n(r):s(r))};function n(){i||(i=!0,r.apply(null,arguments))}function s(t){n(null,t)}}},53278:function(t,e,r){"use strict";var i=r(47529),n=r(35717);t.exports=function(t){var e,r,s;for(r in n(o,t),n(a,o),e=o.prototype)(s=e[r])&&"object"==typeof s&&(e[r]="concat"in s?s.concat():i(s));return o;function a(e){return t.apply(this,e)}function o(){return this instanceof o?t.apply(this,arguments):new a(arguments)}}},18835:function(t,e,r){"use strict";var i=r(94470),n=r(18869),s=r(10939),a=r(28281),o=r(3315),c=r(2580);t.exports=function t(){var e=[],r=a(),x={},y=!1,b=-1;return k.data=function(t,e){return o(t)?2===arguments.length?(m("data",y),x[t]=e,k):u.call(x,t)&&x[t]||null:t?(m("data",y),x=t,k):x},k.freeze=w,k.attachers=e,k.use=function(t){var r;if(m("use",y),null==t);else if("function"==typeof t)o.apply(null,arguments);else{if("object"!=typeof t)throw new Error("Expected usable value, not `"+t+"`");"length"in t?a(t):n(t)}return r&&(x.settings=i(x.settings||{},r)),k;function n(t){a(t.plugins),t.settings&&(r=i(r||{},t.settings))}function s(t){if("function"==typeof t)o(t);else{if("object"!=typeof t)throw new Error("Expected usable value, not `"+t+"`");"length"in t?o.apply(null,t):n(t)}}function a(t){var e,r;if(null==t);else{if("object"!=typeof t||!("length"in t))throw new Error("Expected a list of plugins, not `"+t+"`");for(e=t.length,r=-1;++r<e;)s(t[r])}}function o(t,r){var n=function(t){for(var r,i=e.length,n=-1;++n<i;)if((r=e[n])[0]===t)return r}(t);n?(c(n[1])&&c(r)&&(r=i(n[1],r)),n[1]=r):e.push(h.call(arguments))}},k.parse=function(t){var e,r=s(t);return w(),f("parse",e=k.Parser),p(e)?new e(String(r),r).parse():e(String(r),r)},k.stringify=function(t,e){var r,i=s(e);return w(),d("stringify",r=k.Compiler),g(t),p(r)?new r(t,i).compile():r(t,i)},k.run=_,k.runSync=function(t,e){var r,i=!1;return _(t,e,(function(t,e){i=!0,n(t),r=e})),v("runSync","run",i),r},k.process=A,k.processSync=function(t){var e,r=!1;return w(),f("processSync",k.Parser),d("processSync",k.Compiler),A(e=s(t),(function(t){r=!0,n(t)})),v("processSync","process",r),e},k;function k(){for(var r=t(),n=e.length,s=-1;++s<n;)r.use.apply(null,e[s]);return r.data(i(!0,{},x)),r}function w(){var t,i,n,s;if(y)return k;for(;++b<e.length;)i=(t=e[b])[0],!1!==(n=t[1])&&(!0===n&&(t[1]=void 0),"function"==typeof(s=i.apply(k,t.slice(1)))&&r.use(s));return y=!0,b=1/0,k}function _(t,e,i){if(g(t),w(),i||"function"!=typeof e||(i=e,e=null),!i)return new Promise(n);function n(n,a){r.run(t,s(e),(function(e,r,s){r=r||t,e?a(e):n?n(r):i(null,r,s)}))}n(null,i)}function A(t,e){if(w(),f("process",k.Parser),d("process",k.Compiler),!e)return new Promise(r);function r(r,i){var n=s(t);l.run(k,{file:n},(function(t){t?i(t):r?r(n):e(null,n)}))}r(null,e)}}().freeze();var h=[].slice,u={}.hasOwnProperty,l=a().use((function(t,e){e.tree=t.parse(e.file)})).use((function(t,e,r){t.run(e.tree,e.file,(function(t,i,n){t?r(t):(e.tree=i,e.file=n,r())}))})).use((function(t,e){e.file.contents=t.stringify(e.tree,e.file)}));function p(t){return"function"==typeof t&&function(t){var e;for(e in t)return!0;return!1}(t.prototype)}function f(t,e){if("function"!=typeof e)throw new Error("Cannot `"+t+"` without `Parser`")}function d(t,e){if("function"!=typeof e)throw new Error("Cannot `"+t+"` without `Compiler`")}function m(t,e){if(e)throw new Error(["Cannot invoke `"+t+"` on a frozen processor.\nCreate a new ","processor first, by invoking it: use `processor()` instead of ","`processor`."].join(""))}function g(t){if(!t||!o(t.type))throw new Error("Expected node, got `"+t+"`")}function v(t,e,r){if(!r)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}},48145:function(t){"use strict";function e(t){if("string"==typeof t)return function(t){return function(e){return Boolean(e&&e.type===t)}}(t);if(null==t)return n;if("object"==typeof t)return("length"in t?i:r)(t);if("function"==typeof t)return t;throw new Error("Expected function, string, or object as test")}function r(t){return function(e){var r;for(r in t)if(e[r]!==t[r])return!1;return!0}}function i(t){var r=function(t){for(var r=[],i=t.length,n=-1;++n<i;)r[n]=e(t[n]);return r}(t),i=r.length;return function(){for(var t=-1;++t<i;)if(r[t].apply(this,arguments))return!0;return!1}}function n(){return!0}t.exports=e},33183:function(t,e,r){"use strict";var i=r(62854);function n(t){delete t.position}function s(t){t.position=void 0}t.exports=function(t,e){return i(t,e?n:s),t}},75432:function(t){"use strict";var e={}.hasOwnProperty;function r(t){return t&&"object"==typeof t||(t={}),n(t.line)+":"+n(t.column)}function i(t){return t&&"object"==typeof t||(t={}),r(t.start)+"-"+r(t.end)}function n(t){return t&&"number"==typeof t?t:1}t.exports=function(t){return t&&"object"==typeof t?e.call(t,"position")||e.call(t,"type")?i(t.position):e.call(t,"start")||e.call(t,"end")?i(t):e.call(t,"line")||e.call(t,"column")?r(t):null:null}},99294:function(t){"use strict";t.exports=function(t,e,r){var i=[];"function"==typeof e&&(r=e,e=null),function t(n){var s;return e&&n.type!==e||(s=r(n,i.concat())),n.children&&!1!==s?function(e,r){var n,s=e.length,a=-1;for(i.push(r);++a<s;)if((n=e[a])&&!1===t(n))return!1;return i.pop(),!0}(n.children,n):s}(t)}},62854:function(t,e,r){"use strict";t.exports=o;var i=r(49858),n=i.CONTINUE,s=i.SKIP,a=i.EXIT;function o(t,e,r,n){"function"==typeof e&&"function"!=typeof r&&(n=r,r=e,e=null),i(t,e,(function(t,e){var i=e[e.length-1],n=i?i.children.indexOf(t):null;return r(t,n,i)}),n)}o.CONTINUE=n,o.SKIP=s,o.EXIT=a},49858:function(t,e,r){"use strict";t.exports=o;var i=r(48145),n=!0,s="skip",a=!1;function o(t,e,r,n){var o;"function"==typeof e&&"function"!=typeof r&&(n=r,r=e,e=null),o=i(e),function t(i,h,u){var l,p=[];return(e&&!o(i,h,u[u.length-1]||null)||(p=c(r(i,u)))[0]!==a)&&i.children&&p[0]!==s?(l=c(function(e,r){for(var i,s=n?-1:1,o=(n?e.length:-1)+s;o>-1&&o<e.length;){if((i=t(e[o],o,r))[0]===a)return i;o="number"==typeof i[1]?i[1]:o+s}}(i.children,u.concat(i))),l[0]===a?l:p):p}(t,null,[])}function c(t){return null!==t&&"object"==typeof t&&"length"in t?t:"number"==typeof t?[n,t]:[t]}o.CONTINUE=n,o.SKIP=s,o.EXIT=a},14787:function(t){"use strict";function e(t){return function(e){var r=e&&e.line,i=e&&e.column;return isNaN(r)||isNaN(i)||!(r-1 in t)?-1:(t[r-2]||0)+i-1||0}}t.exports=function(t){var r,i=function(t){for(var e=[],r=t.indexOf("\n");-1!==r;)e.push(r+1),r=t.indexOf("\n",r+1);return e.push(t.length+1),e}(String(t));return{toPosition:(r=i,function(t){var e=-1,i=r.length;if(t<0)return{};for(;++e<i;)if(r[e]>t)return{line:e+1,column:t-(r[e-1]||0)+1,offset:t};return{}}),toOffset:e(i)}}},80734:function(t,e,r){"use strict";var i=r(75432);function n(){}t.exports=a,n.prototype=Error.prototype,a.prototype=new n;var s=a.prototype;function a(t,e,r){var n,s,a;"string"==typeof e&&(r=e,e=null),n=function(t){var e,r=[null,null];return"string"==typeof t&&(-1===(e=t.indexOf(":"))?r[1]=t:(r[0]=t.slice(0,e),r[1]=t.slice(e+1))),r}(r),s=i(e)||"1:1",a={start:{line:null,column:null},end:{line:null,column:null}},e&&e.position&&(e=e.position),e&&(e.start?(a=e,e=e.start):a.start=e),t.stack&&(this.stack=t.stack,t=t.message),this.message=t,this.name=s,this.reason=t,this.line=e?e.line:null,this.column=e?e.column:null,this.location=a,this.source=n[0],this.ruleId=n[1]}s.file="",s.name="",s.reason="",s.message="",s.stack="",s.fatal=null,s.column=null,s.line=null},63638:function(t,e,r){"use strict";var i=r(34155),n=r(26470),s=r(38245),a=r(48738);t.exports=u;var o={}.hasOwnProperty,c=u.prototype;c.toString=function(t){var e=this.contents||"";return a(e)?e.toString(t):String(e)};var h=["history","path","basename","stem","extname","dirname"];function u(t){var e,r,n;if(t){if("string"==typeof t||a(t))t={contents:t};else if("message"in t&&"messages"in t)return t}else t={};if(!(this instanceof u))return new u(t);for(this.data={},this.messages=[],this.history=[],this.cwd=i.cwd(),r=-1,n=h.length;++r<n;)e=h[r],o.call(t,e)&&(this[e]=t[e]);for(e in t)-1===h.indexOf(e)&&(this[e]=t[e])}function l(t,e){if(-1!==t.indexOf(n.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+n.sep+"`")}function p(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function f(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}Object.defineProperty(c,"path",{get:function(){return this.history[this.history.length-1]},set:function(t){p(t,"path"),t!==this.path&&this.history.push(t)}}),Object.defineProperty(c,"dirname",{get:function(){return"string"==typeof this.path?n.dirname(this.path):void 0},set:function(t){f(this.path,"dirname"),this.path=n.join(t||"",this.basename)}}),Object.defineProperty(c,"basename",{get:function(){return"string"==typeof this.path?n.basename(this.path):void 0},set:function(t){p(t,"basename"),l(t,"basename"),this.path=n.join(this.dirname||"",t)}}),Object.defineProperty(c,"extname",{get:function(){return"string"==typeof this.path?n.extname(this.path):void 0},set:function(t){var e=t||"";if(l(e,"extname"),f(this.path,"extname"),e){if("."!==e.charAt(0))throw new Error("`extname` must start with `.`");if(-1!==e.indexOf(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=s(this.path,e)}}),Object.defineProperty(c,"stem",{get:function(){return"string"==typeof this.path?n.basename(this.path,this.extname):void 0},set:function(t){p(t,"stem"),l(t,"stem"),this.path=n.join(this.dirname||"",t+(this.extname||""))}})},10939:function(t,e,r){"use strict";var i=r(80734),n=r(63638);t.exports=n;var s=n.prototype;function a(t,e,r){var n=this.path,s=new i(t,e,r);return n&&(s.name=n+":"+s.name,s.file=n),s.fatal=!1,this.messages.push(s),s}s.message=a,s.info=function(){var t=this.message.apply(this,arguments);return t.fatal=null,t},s.fail=function(){var t=this.message.apply(this,arguments);throw t.fatal=!0,t},s.warn=a},3315:function(t){var e=Object.prototype.toString;t.exports=function(t){return"[object String]"===e.call(t)}},47529:function(t){t.exports=function(){for(var t={},r=0;r<arguments.length;r++){var i=arguments[r];for(var n in i)e.call(i,n)&&(t[n]=i[n])}return t};var e=Object.prototype.hasOwnProperty},37452:function(t){"use strict";t.exports=JSON.parse('{"AElig":"Æ","AMP":"&","Aacute":"Á","Acirc":"Â","Agrave":"À","Aring":"Å","Atilde":"Ã","Auml":"Ä","COPY":"©","Ccedil":"Ç","ETH":"Ð","Eacute":"É","Ecirc":"Ê","Egrave":"È","Euml":"Ë","GT":">","Iacute":"Í","Icirc":"Î","Igrave":"Ì","Iuml":"Ï","LT":"<","Ntilde":"Ñ","Oacute":"Ó","Ocirc":"Ô","Ograve":"Ò","Oslash":"Ø","Otilde":"Õ","Ouml":"Ö","QUOT":"\\"","REG":"®","THORN":"Þ","Uacute":"Ú","Ucirc":"Û","Ugrave":"Ù","Uuml":"Ü","Yacute":"Ý","aacute":"á","acirc":"â","acute":"´","aelig":"æ","agrave":"à","amp":"&","aring":"å","atilde":"ã","auml":"ä","brvbar":"¦","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","curren":"¤","deg":"°","divide":"÷","eacute":"é","ecirc":"ê","egrave":"è","eth":"ð","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","iacute":"í","icirc":"î","iexcl":"¡","igrave":"ì","iquest":"¿","iuml":"ï","laquo":"«","lt":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","ntilde":"ñ","oacute":"ó","ocirc":"ô","ograve":"ò","ordf":"ª","ordm":"º","oslash":"ø","otilde":"õ","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","raquo":"»","reg":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","thorn":"þ","times":"×","uacute":"ú","ucirc":"û","ugrave":"ù","uml":"¨","uuml":"ü","yacute":"ý","yen":"¥","yuml":"ÿ"}')},93580:function(t){"use strict";t.exports=JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}')},94864:function(t){"use strict";t.exports=JSON.parse('["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","legend","li","link","main","menu","menuitem","meta","nav","noframes","ol","optgroup","option","p","param","pre","section","source","title","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]')}}]);
//# sourceMappingURL=async-markdown.js.map