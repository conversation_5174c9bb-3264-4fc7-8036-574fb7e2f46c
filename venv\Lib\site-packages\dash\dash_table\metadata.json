{"src/dash-table/dash/DataTable.js": {"description": "Dash DataTable is an interactive table component designed for\nviewing, editing, and exploring large datasets.\nDataTable is rendered with standard, semantic HTML <table/> markup,\nwhich makes it accessible, responsive, and easy to style. This\ncomponent was written from scratch in React.js specifically for the\nDash community. Its API was designed to be ergonomic and its behavior\nis completely customizable through its properties.", "displayName": "DataTable", "methods": [], "props": {"data": {"type": {"name": "arrayOf", "value": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}}}, "required": false, "description": "The contents of the table.\nThe keys of each item in data should match the column IDs.\nEach item can also have an 'id' key, whose value is its row ID. If there\nis a column with ID='id' this will display the row ID, otherwise it is\njust used to reference the row for selections, filtering, etc.\nExample:\n[\n     {'column-1': 4.5, 'column-2': 'montreal', 'column-3': 'canada'},\n     {'column-1': 8, 'column-2': 'boston', 'column-3': 'america'}\n]"}, "columns": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"id": {"name": "string", "description": "The `id` of the column.\nThe column `id` is used to match cells in data with particular columns.\nThe `id` is not visible in the table.", "required": true}, "name": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}], "description": "The `name` of the column, as it appears in the column header.\nIf `name` is a list of strings, then the columns\nwill render with multiple headers rows.", "required": true}, "type": {"name": "enum", "value": [{"value": "'any'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'text'", "computed": false}, {"value": "'datetime'", "computed": false}], "description": "The data-type provides support for per column typing and allows for data\nvalidation and coercion.\n'numeric': represents both floats and ints.\n'text': represents a string.\n'datetime': a string representing a date or date-time, in the form:\n  'YYYY-MM-DD HH:MM:SS.ssssss' or some truncation thereof. Years must\n  have 4 digits, unless you use `validation.allow_YY: true`. Also\n  accepts 'T' or 't' between date and time, and allows timezone info\n  at the end. To convert these strings to Python `datetime` objects,\n  use `dateutil.parser.isoparse`. In R use `parse_iso_8601` from the\n  `parsedate` library.\n  WARNING: these parsers do not work with 2-digit years, if you use\n  `validation.allow_YY: true` and do not coerce to 4-digit years.\n  And parsers that do work with 2-digit years may make a different\n  guess about the century than we make on the front end.\n'any': represents any type of data.\nDefaults to 'any' if undefined.", "required": false}, "presentation": {"name": "enum", "value": [{"value": "'input'", "computed": false}, {"value": "'dropdown'", "computed": false}, {"value": "'markdown'", "computed": false}], "description": "The `presentation` to use to display data. Markdown can be used on\ncolumns with type 'text'.  See 'dropdown' for more info.\nDefaults to 'input' for ['datetime', 'numeric', 'text', 'any'].", "required": false}, "selectable": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'first'", "computed": false}, {"value": "'last'", "computed": false}]}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "bool"}}], "description": "If true, the user can select the column by clicking on the checkbox or radio button\nin the column. If there are multiple header rows, true will display the input\non each row.\nIf `last`, the input will only appear on the last header row. If `first` it will only\nappear on the first header row. These are respectively shortcut equivalents to\n`[false, ..., false, true]` and `[true, false, ..., false]`.\nIf there are merged, multi-header columns then you can choose which column header\nrow to display the input in by supplying an array of booleans.\nFor example, `[true, false]` will display the `selectable` input on the first row,\nbut now on the second row.\nIf the `selectable` input appears on a merged columns, then clicking on that input\nwill select *all* of the merged columns associated with it.\nThe table-level prop `column_selectable` is used to determine the type of column\nselection to use.", "required": false}, "clearable": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'first'", "computed": false}, {"value": "'last'", "computed": false}]}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "bool"}}], "description": "If true, the user can clear the column by clicking on the `clear`\naction button on the column. If there are multiple header rows, true\nwill display the action button on each row.\nIf `last`, the `clear` action button will only appear on the last header\nrow. If `first` it will only appear on the first header row. These\nare respectively shortcut equivalents to `[false, ..., false, true]` and\n`[true, false, ..., false]`.\nIf there are merged, multi-header columns then you can choose\nwhich column header row to display the `clear` action button in by\nsupplying an array of booleans.\nFor example, `[true, false]` will display the `clear` action button\non the first row, but not the second row.\nIf the `clear` action button appears on a merged column, then clicking\non that button will clear *all* of the merged columns associated with it.\nUnlike `column.deletable`, this action does not remove the column(s)\nfrom the table. It only removed the associated entries from `data`.", "required": false}, "deletable": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'first'", "computed": false}, {"value": "'last'", "computed": false}]}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "bool"}}], "description": "If true, the user can remove the column by clicking on the `delete`\naction button on the column. If there are multiple header rows, true\nwill display the action button on each row.\nIf `last`, the `delete` action button will only appear on the last header\nrow. If `first` it will only appear on the first header row. These\nare respectively shortcut equivalents to `[false, ..., false, true]` and\n`[true, false, ..., false]`.\nIf there are merged, multi-header columns then you can choose\nwhich column header row to display the `delete` action button in by\nsupplying an array of booleans.\nFor example, `[true, false]` will display the `delete` action button\non the first row, but not the second row.\nIf the `delete` action button appears on a merged column, then clicking\non that button will remove *all* of the merged columns associated with it.", "required": false}, "editable": {"name": "bool", "description": "There are two `editable` flags in the table.\nThis is the  column-level editable flag and there is\nalso the table-level `editable` flag.\nThese flags determine whether the contents of the table\nare editable or not.\nIf the column-level `editable` flag is set it overrides\nthe table-level `editable` flag for that column.", "required": false}, "hideable": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'first'", "computed": false}, {"value": "'last'", "computed": false}]}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "bool"}}], "description": "If true, the user can hide the column by clicking on the `hide`\naction button on the column. If there are multiple header rows, true\nwill display the action button on each row.\nIf `last`, the `hide` action button will only appear on the last header\nrow. If `first` it will only appear on the first header row. These\nare respectively shortcut equivalents to `[false, ..., false, true]` and\n`[true, false, ..., false]`.\nIf there are merged, multi-header columns then you can choose\nwhich column header row to display the `hide` action button in by\nsupplying an array of booleans.\nFor example, `[true, false]` will display the `hide` action button\non the first row, but not the second row.\nIf the `hide` action button appears on a merged column, then clicking\non that button will hide *all* of the merged columns associated with it.", "required": false}, "renamable": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'first'", "computed": false}, {"value": "'last'", "computed": false}]}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "bool"}}], "description": "If true, the user can rename the column by clicking on the `rename`\naction button on the column. If there are multiple header rows, true\nwill display the action button on each row.\nIf `last`, the `rename` action button will only appear on the last header\nrow. If `first` it will only appear on the first header row. These\nare respectively shortcut equivalents to `[false, ..., false, true]` and\n`[true, false, ..., false]`.\nIf there are merged, multi-header columns then you can choose\nwhich column header row to display the `rename` action button in by\nsupplying an array of booleans.\nFor example, `[true, false]` will display the `rename` action button\non the first row, but not the second row.\nIf the `rename` action button appears on a merged column, then clicking\non that button will rename *all* of the merged columns associated with it.", "required": false}, "filter_options": {"name": "shape", "value": {"case": {"name": "enum", "value": [{"value": "'sensitive'", "computed": false}, {"value": "'insensitive'", "computed": false}], "description": "(default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.", "required": false}, "placeholder_text": {"name": "string", "description": "(default: 'filter data...') The filter cell placeholder text.", "required": false}}, "description": "There are two `filter_options` props in the table.\nThis is the column-level filter_options prop and there is\nalso the table-level `filter_options` prop.\nIf the column-level `filter_options` prop is set it overrides\nthe table-level `filter_options` prop for that column.", "required": false}, "format": {"name": "exact", "value": {"locale": {"name": "exact", "value": {"symbol": {"name": "arrayOf", "value": {"name": "string"}, "description": "(default: ['$', '']).  A list of two strings representing the\n prefix and suffix symbols. Typically used for currency, and implemented using d3's\n currency format, but you can use this for other symbols such as measurement units", "required": false}, "decimal": {"name": "string", "description": "(default: '.').  The string used for the decimal separator", "required": false}, "group": {"name": "string", "description": "(default: ',').  The string used for the groups separator", "required": false}, "grouping": {"name": "arrayOf", "value": {"name": "number"}, "description": "(default: [3]).  A list of integers representing the grouping pattern. The default is\n3 for thousands.", "required": false}, "numerals": {"name": "arrayOf", "value": {"name": "string"}, "description": "A list of ten strings used as replacements for numbers 0-9", "required": false}, "percent": {"name": "string", "description": "(default: '%').  The string used for the percentage symbol", "required": false}, "separate_4digits": {"name": "bool", "description": "(default: True). Separates integers with 4-digits or less", "required": false}}, "description": "Represents localization specific formatting information.\nWhen left unspecified, will use the default value provided by d3-format.", "required": false}, "nully": {"name": "any", "description": "A value that will be used in place of the nully value during formatting.\n  If the value type matches the column type, it will be formatted normally.", "required": false}, "prefix": {"name": "number", "description": "A number representing the SI unit to use during formatting.\n  See `dash_table.Format.Prefix` enumeration for the list of valid values", "required": false}, "specifier": {"name": "string", "description": "(default: '').  Represents the d3 rules to apply when formatting the number.", "required": false}}, "description": "The formatting applied to the column's data.\nThis prop is derived from the [d3-format](https://github.com/d3/d3-format) library specification. Apart from\nbeing structured slightly differently (under a single prop), the usage is the same.\nSee also dash_table.FormatTemplate.  It contains helper functions for typical number formats.", "required": false}, "on_change": {"name": "exact", "value": {"action": {"name": "enum", "value": [{"value": "'coerce'", "computed": false}, {"value": "'none'", "computed": false}, {"value": "'validate'", "computed": false}], "description": "(default 'coerce'):  'none': do not validate data;\n 'coerce': check if the data corresponds to the destination type and\n attempts to coerce it into the destination type if not;\n 'validate': check if the data corresponds to the destination type (no coercion).", "required": false}, "failure": {"name": "enum", "value": [{"value": "'accept'", "computed": false}, {"value": "'default'", "computed": false}, {"value": "'reject'", "computed": false}], "description": "(default 'reject'):  What to do with the value if the action fails.\n 'accept': use the invalid value;\n 'default': replace the provided value with `validation.default`;\n 'reject': do not modify the existing value.", "required": false}}, "description": "The `on_change` behavior of the column for user-initiated modifications.", "required": false}, "sort_as_null": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}, "description": "An array of string, number and boolean values that are treated as `null`\n(i.e. ignored and always displayed last) when sorting.\nThis value overrides the table-level `sort_as_null`.", "required": false}, "validation": {"name": "exact", "value": {"allow_null": {"name": "bool", "description": "Allow the use of nully values. (undefined, null, NaN) (default: False)", "required": false}, "default": {"name": "any", "description": "The default value to apply with on_change.failure = 'default'. (default: None)", "required": false}, "allow_YY": {"name": "bool", "description": "This is for `datetime` columns only.  Allow 2-digit years (default: False).\n  If True, we interpret years as ranging from now-70 to now+29 - in 2019\n  this is 1949 to 2048 but in 2020 it will be different. If used with\n  `action: 'coerce'`, will convert user input to a 4-digit year.", "required": false}}, "description": "The `validation` options for user input processing that can accept, reject or apply a\ndefault value.", "required": false}}}}, "required": false, "description": "Columns describes various aspects about each individual column.\n`name` and `id` are the only required parameters."}, "editable": {"type": {"name": "bool"}, "required": false, "description": "If True, then the data in all of the cells is editable.\nWhen `editable` is True, particular columns can be made\nuneditable by setting `editable` to `False` inside the `columns`\nproperty.\nIf False, then the data in all of the cells is uneditable.\nWhen `editable` is False, particular columns can be made\neditable by setting `editable` to `True` inside the `columns`\nproperty.", "defaultValue": {"value": "false", "computed": false}}, "fixed_columns": {"type": {"name": "union", "value": [{"name": "exact", "value": {"data": {"name": "enum", "value": [{"value": "0", "computed": false}], "description": "Example `{'headers':False, 'data':0}` No columns are fixed (the default)", "required": false}, "headers": {"name": "enum", "value": [{"value": "false", "computed": false}], "required": false}}}, {"name": "exact", "value": {"data": {"name": "number", "description": "Example `{'headers':True, 'data':1}` one column is fixed.", "required": false}, "headers": {"name": "enum", "value": [{"value": "true", "computed": false}], "required": true}}}]}, "required": false, "description": "`fixed_columns` will \"fix\" the set of columns so that\nthey remain visible when scrolling horizontally across\nthe unfixed columns. `fixed_columns` fixes columns\nfrom left-to-right.\nIf `headers` is False, no columns are fixed.\nIf `headers` is True, all operation columns (see `row_deletable` and `row_selectable`)\nare fixed. Additional data columns can be fixed by\nassigning a number to `data`.\n\nNote that fixing columns introduces some changes to the\nunderlying markup of the table and may impact the\nway that your columns are rendered or sized.\nView the documentation examples to learn more.", "defaultValue": {"value": "{\n    headers: false,\n    data: 0\n}", "computed": false}}, "fixed_rows": {"type": {"name": "union", "value": [{"name": "exact", "value": {"data": {"name": "enum", "value": [{"value": "0", "computed": false}], "description": "Example `{'headers':False, 'data':0}` No rows are fixed (the default)", "required": false}, "headers": {"name": "enum", "value": [{"value": "false", "computed": false}], "required": false}}}, {"name": "exact", "value": {"data": {"name": "number", "description": "Example `{'headers':True, 'data':1}` one row is fixed.", "required": false}, "headers": {"name": "enum", "value": [{"value": "true", "computed": false}], "required": true}}}]}, "required": false, "description": "`fixed_rows` will \"fix\" the set of rows so that\nthey remain visible when scrolling vertically down\nthe table. `fixed_rows` fixes rows\nfrom top-to-bottom, starting from the headers.\nIf `headers` is False, no rows are fixed.\nIf `headers` is True, all header and filter rows (see `filter_action`) are\nfixed. Additional data rows can be fixed by assigning\na number to `data`.  Note that fixing rows introduces some changes to the\nunderlying markup of the table and may impact the\nway that your columns are rendered or sized.\nView the documentation examples to learn more.", "defaultValue": {"value": "{\n    headers: false,\n    data: 0\n}", "computed": false}}, "column_selectable": {"type": {"name": "enum", "value": [{"value": "'single'", "computed": false}, {"value": "'multi'", "computed": false}, {"value": "false", "computed": false}]}, "required": false, "description": "If `single`, then the user can select a single column or group\nof merged columns via the radio button that will appear in the\nheader rows.\nIf `multi`, then the user can select multiple columns or groups\nof merged columns via the checkbox that will appear in the header\nrows.\nIf false, then the user will not be able to select columns and no\ninput will appear in the header rows.\nWhen a column is selected, its id will be contained in `selected_columns`\nand `derived_viewport_selected_columns`.", "defaultValue": {"value": "false", "computed": false}}, "cell_selectable": {"type": {"name": "bool"}, "required": false, "description": "If True (default), then it is possible to click and navigate\ntable cells.", "defaultValue": {"value": "true", "computed": false}}, "row_selectable": {"type": {"name": "enum", "value": [{"value": "'single'", "computed": false}, {"value": "'multi'", "computed": false}, {"value": "false", "computed": false}]}, "required": false, "description": "If `single`, then the user can select a single row\nvia a radio button that will appear next to each row.\nIf `multi`, then the user can select multiple rows\nvia a checkbox that will appear next to each row.\nIf false, then the user will not be able to select rows\nand no additional UI elements will appear.\nWhen a row is selected, its index will be contained\nin `selected_rows`.", "defaultValue": {"value": "false", "computed": false}}, "row_deletable": {"type": {"name": "bool"}, "required": false, "description": "If True, then a `x` will appear next to each `row`\nand the user can delete the row."}, "active_cell": {"type": {"name": "exact", "value": {"row": {"name": "number", "required": false}, "column": {"name": "number", "required": false}, "row_id": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "required": false}, "column_id": {"name": "string", "required": false}}}, "required": false, "description": "The row and column indices and IDs of the currently active cell.\n`row_id` is only returned if the data rows have an `id` key."}, "selected_cells": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"row": {"name": "number", "required": false}, "column": {"name": "number", "required": false}, "row_id": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "required": false}, "column_id": {"name": "string", "required": false}}}}, "required": false, "description": "`selected_cells` represents the set of cells that are selected,\nas an array of objects, each item similar to `active_cell`.\nMultiple cells can be selected by holding down shift and\nclicking on a different cell or holding down shift and navigating\nwith the arrow keys.", "defaultValue": {"value": "[]", "computed": false}}, "selected_rows": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "`selected_rows` contains the indices of rows that\nare selected via the UI elements that appear when\n`row_selectable` is `'single'` or `'multi'`.", "defaultValue": {"value": "[]", "computed": false}}, "selected_columns": {"type": {"name": "arrayOf", "value": {"name": "string"}}, "required": false, "description": "`selected_columns` contains the ids of columns that\nare selected via the UI elements that appear when\n`column_selectable` is `'single' or 'multi'`.", "defaultValue": {"value": "[]", "computed": false}}, "selected_row_ids": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "`selected_row_ids` contains the ids of rows that\nare selected via the UI elements that appear when\n`row_selectable` is `'single'` or `'multi'`.", "defaultValue": {"value": "[]", "computed": false}}, "start_cell": {"type": {"name": "exact", "value": {"row": {"name": "number", "required": false}, "column": {"name": "number", "required": false}, "row_id": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "required": false}, "column_id": {"name": "string", "required": false}}}, "required": false, "description": "When selecting multiple cells\n(via clicking on a cell and then shift-clicking on another cell),\n`start_cell` represents the [row, column] coordinates of the cell\nin one of the corners of the region.\n`end_cell` represents the coordinates of the other corner."}, "end_cell": {"type": {"name": "exact", "value": {"row": {"name": "number", "required": false}, "column": {"name": "number", "required": false}, "row_id": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "required": false}, "column_id": {"name": "string", "required": false}}}, "required": false, "description": "When selecting multiple cells\n(via clicking on a cell and then shift-clicking on another cell),\n`end_cell` represents the row / column coordinates and IDs of the cell\nin one of the corners of the region.\n`start_cell` represents the coordinates of the other corner."}, "data_previous": {"type": {"name": "arrayOf", "value": {"name": "object"}}, "required": false, "description": "The previous state of `data`. `data_previous`\nhas the same structure as `data` and it will be updated\nwhenever `data` changes, either through a callback or\nby editing the table.\nThis is a read-only property: setting this property will not\nhave any impact on the table."}, "hidden_columns": {"type": {"name": "arrayOf", "value": {"name": "string"}}, "required": false, "description": "List of columns ids of the columns that are currently hidden.\nSee the associated nested prop `columns.hideable`."}, "is_focused": {"type": {"name": "bool"}, "required": false, "description": "If True, then the `active_cell` is in a focused state."}, "merge_duplicate_headers": {"type": {"name": "bool"}, "required": false, "description": "If True, then column headers that have neighbors with duplicate names\nwill be merged into a single cell.\nThis will be applied for single column headers and multi-column\nheaders."}, "data_timestamp": {"type": {"name": "number"}, "required": false, "description": "The unix timestamp when the data was last edited.\nUse this property with other timestamp properties\n(such as `n_clicks_timestamp` in `dash_html_components`)\nto determine which property has changed within a callback."}, "include_headers_on_copy_paste": {"type": {"name": "bool"}, "required": false, "description": "If true, headers are included when copying from the table to different\ntabs and elsewhere. Note that headers are ignored when copying from the table onto itself and\nbetween two tables within the same tab.", "defaultValue": {"value": "false", "computed": false}}, "export_columns": {"type": {"name": "enum", "value": [{"value": "'all'", "computed": false}, {"value": "'visible'", "computed": false}]}, "required": false, "description": "Denotes the columns that will be used in the export data file.\nIf `all`, all columns will be used (visible + hidden). If `visible`,\nonly the visible columns will be used. Defaults to `visible`.", "defaultValue": {"value": "'visible'", "computed": false}}, "export_format": {"type": {"name": "enum", "value": [{"value": "'csv'", "computed": false}, {"value": "'xlsx'", "computed": false}, {"value": "'none'", "computed": false}]}, "required": false, "description": "Denotes the type of the export data file,\nDefaults to `'none'`", "defaultValue": {"value": "'none'", "computed": false}}, "export_headers": {"type": {"name": "enum", "value": [{"value": "'none'", "computed": false}, {"value": "'ids'", "computed": false}, {"value": "'names'", "computed": false}, {"value": "'display'", "computed": false}]}, "required": false, "description": "Denotes the format of the headers in the export data file.\nIf `'none'`, there will be no header. If `'display'`, then the header\nof the data file will be be how it is currently displayed. Note that\n`'display'` is only supported for `'xlsx'` export_format and will behave\nlike `'names'` for `'csv'` export format. If `'ids'` or `'names'`,\nthen the headers of data file will be the column id or the column\nnames, respectively"}, "page_action": {"type": {"name": "enum", "value": [{"value": "'custom'", "computed": false}, {"value": "'native'", "computed": false}, {"value": "'none'", "computed": false}]}, "required": false, "description": "`page_action` refers to a mode of the table where\nnot all of the rows are displayed at once: only a subset\nare displayed (a \"page\") and the next subset of rows\ncan viewed by clicking \"Next\" or \"Previous\" buttons\nat the bottom of the page.\nPagination is used to improve performance: instead of\nrendering all of the rows at once (which can be expensive),\nwe only display a subset of them.\nWith pagination, we can either page through data that exists\nin the table (e.g. page through `10,000` rows in `data` `100` rows at a time)\nor we can update the data on-the-fly with callbacks\nwhen the user clicks on the \"Previous\" or \"Next\" buttons.\nThese modes can be toggled with this `page_action` parameter:\n`'native'`: all data is passed to the table up-front, paging logic is\nhandled by the table;\n`'custom'`: data is passed to the table one page at a time, paging logic\nis handled via callbacks;\n`'none'`: disables paging, render all of the data at once.", "defaultValue": {"value": "'native'", "computed": false}}, "page_current": {"type": {"name": "number"}, "required": false, "description": "`page_current` represents which page the user is on.\nUse this property to index through data in your callbacks with\nbackend paging.", "defaultValue": {"value": "0", "computed": false}}, "page_count": {"type": {"name": "number"}, "required": false, "description": "`page_count` represents the number of the pages in the\npaginated table. This is really only useful when performing\nbackend pagination, since the front end is able to use the\nfull size of the table to calculate the number of pages."}, "page_size": {"type": {"name": "number"}, "required": false, "description": "`page_size` represents the number of rows that will be\ndisplayed on a particular page when `page_action` is `'custom'` or `'native'`", "defaultValue": {"value": "250", "computed": false}}, "filter_query": {"type": {"name": "string"}, "required": false, "description": "If `filter_action` is enabled, then the current filtering\nstring is represented in this `filter_query`\nproperty.", "defaultValue": {"value": "''", "computed": false}}, "filter_action": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'custom'", "computed": false}, {"value": "'native'", "computed": false}, {"value": "'none'", "computed": false}]}, {"name": "shape", "value": {"type": {"name": "enum", "value": [{"value": "'custom'", "computed": false}, {"value": "'native'", "computed": false}], "required": true}, "operator": {"name": "enum", "value": [{"value": "'and'", "computed": false}, {"value": "'or'", "computed": false}], "required": false}}}]}, "required": false, "description": "The `filter_action` property controls the behavior of the `filtering` UI.\nIf `'none'`, then the filtering UI is not displayed.\nIf `'native'`, then the filtering UI is displayed and the filtering\nlogic is handled by the table. That is, it is performed on the data\nthat exists in the `data` property.\nIf `'custom'`, then the filtering UI is displayed but it is the\nresponsibility of the developer to program the filtering\nthrough a callback (where `filter_query` or `derived_filter_query_structure` would be the input\nand `data` would be the output).", "defaultValue": {"value": "'none'", "computed": false}}, "filter_options": {"type": {"name": "shape", "value": {"case": {"name": "enum", "value": [{"value": "'sensitive'", "computed": false}, {"value": "'insensitive'", "computed": false}], "description": "(default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.", "required": false}, "placeholder_text": {"name": "string", "description": "(default: 'filter data...') The filter cell placeholder text.", "required": false}}}, "required": false, "description": "There are two `filter_options` props in the table.\nThis is the table-level filter_options prop and there is\nalso the column-level `filter_options` prop.\nIf the column-level `filter_options` prop is set it overrides\nthe table-level `filter_options` prop for that column.", "defaultValue": {"value": "{}", "computed": false}}, "sort_action": {"type": {"name": "enum", "value": [{"value": "'custom'", "computed": false}, {"value": "'native'", "computed": false}, {"value": "'none'", "computed": false}]}, "required": false, "description": "The `sort_action` property enables data to be\nsorted on a per-column basis.\nIf `'none'`, then the sorting UI is not displayed.\nIf `'native'`, then the sorting UI is displayed and the sorting\nlogic is handled by the table. That is, it is performed on the data\nthat exists in the `data` property.\nIf `'custom'`, the the sorting UI is displayed but it is the\nresponsibility of the developer to program the sorting\nthrough a callback (where `sort_by` would be the input and `data`\nwould be the output).\nClicking on the sort arrows will update the\n`sort_by` property.", "defaultValue": {"value": "'none'", "computed": false}}, "sort_mode": {"type": {"name": "enum", "value": [{"value": "'single'", "computed": false}, {"value": "'multi'", "computed": false}]}, "required": false, "description": "Sorting can be performed across multiple columns\n(e.g. sort by country, sort within each country,\n sort by year) or by a single column.\nNOTE - With multi-column sort, it's currently\nnot possible to determine the order in which\nthe columns were sorted through the UI.\nSee [https://github.com/plotly/dash-table/issues/170](https://github.com/plotly/dash-table/issues/170)", "defaultValue": {"value": "'single'", "computed": false}}, "sort_by": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"column_id": {"name": "string", "required": true}, "direction": {"name": "enum", "value": [{"value": "'asc'", "computed": false}, {"value": "'desc'", "computed": false}], "required": true}}}}, "required": false, "description": "`sort_by` describes the current state\nof the sorting UI.\nThat is, if the user clicked on the sort arrow\nof a column, then this property will be updated\nwith the column ID and the direction\n(`asc` or `desc`) of the sort.\nFor multi-column sorting, this will be a list of\nsorting parameters, in the order in which they were\nclicked.", "defaultValue": {"value": "[]", "computed": false}}, "sort_as_null": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}}, "required": false, "description": "An array of string, number and boolean values that are treated as `None`\n(i.e. ignored and always displayed last) when sorting.\nThis value will be used by columns without `sort_as_null`.\nDefaults to `[]`.", "defaultValue": {"value": "[]", "computed": false}}, "dropdown": {"type": {"name": "objectOf", "value": {"name": "exact", "value": {"clearable": {"name": "bool", "required": false}, "options": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "string", "required": true}, "value": {"name": "union", "value": [{"name": "number"}, {"name": "string"}, {"name": "bool"}], "required": true}}}, "required": true}}}}, "required": false, "description": "`dropdown` specifies dropdown options for different columns.\nEach entry refers to the column ID.\nThe `clearable` property defines whether the value can be deleted.\nThe `options` property refers to the `options` of the dropdown.", "defaultValue": {"value": "{}", "computed": false}}, "dropdown_conditional": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"clearable": {"name": "bool", "required": false}, "if": {"name": "exact", "value": {"column_id": {"name": "string", "required": false}, "filter_query": {"name": "string", "required": false}}, "required": false}, "options": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "string", "required": true}, "value": {"name": "union", "value": [{"name": "number"}, {"name": "string"}, {"name": "bool"}], "required": true}}}, "required": true}}}}, "required": false, "description": "`dropdown_conditional` specifies dropdown options in various columns and cells.\nThis property allows you to specify different dropdowns\ndepending on certain conditions. For example, you may\nrender different \"city\" dropdowns in a row depending on the\ncurrent value in the \"state\" column.", "defaultValue": {"value": "[]", "computed": false}}, "dropdown_data": {"type": {"name": "arrayOf", "value": {"name": "objectOf", "value": {"name": "exact", "value": {"clearable": {"name": "bool", "required": false}, "options": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "string", "required": true}, "value": {"name": "union", "value": [{"name": "number"}, {"name": "string"}, {"name": "bool"}], "required": true}}}, "required": true}}}}}, "required": false, "description": "`dropdown_data` specifies dropdown options on a row-by-row, column-by-column basis.\nEach item in the array corresponds to the corresponding dropdowns for the `data` item\nat the same index. Each entry in the item refers to the Column ID.", "defaultValue": {"value": "[]", "computed": false}}, "tooltip": {"type": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "exact", "value": {"delay": {"name": "number", "description": "Represents the delay in milliseconds before\nthe tooltip is shown when hovering a cell. This overrides\nthe table's `tooltip_delay` property. If set to `None`,\nthe tooltip will be shown immediately.", "required": false}, "duration": {"name": "number", "description": "represents the duration in milliseconds\nduring which the tooltip is shown when hovering a cell.\nThis overrides the table's `tooltip_duration` property.\nIf set to `None`, the tooltip will not disappear.", "required": false}, "type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'markdown'", "computed": false}], "description": "refers to the type of tooltip syntax used\nfor the tooltip generation. Can either be `markdown`\nor `text`. Defaults to `text`.", "required": false}, "use_with": {"name": "enum", "value": [{"value": "'both'", "computed": false}, {"value": "'data'", "computed": false}, {"value": "'header'", "computed": false}], "description": "Refers to whether the tooltip will be shown\nonly on data or headers. Can be `both`, `data`, `header`.\nDefaults to `both`.", "required": false}, "value": {"name": "string", "description": "refers to the syntax-based content of\nthe tooltip. This value is required. Alternatively, the value of the\nproperty can also be  a plain string. The `text` syntax will be used in\nthat case.", "required": true}}}]}}, "required": false, "description": "`tooltip` is the column based tooltip configuration applied to all rows. The key is the column\n id and the value is a tooltip configuration.\nExample: {i: {'value': i, 'use_with: 'both'} for i in df.columns}", "defaultValue": {"value": "{}", "computed": false}}, "tooltip_conditional": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"delay": {"name": "number", "description": "The `delay` represents the delay in milliseconds before\nthe tooltip is shown when hovering a cell. This overrides\nthe table's `tooltip_delay` property. If set to `None`,\nthe tooltip will be shown immediately.", "required": false}, "duration": {"name": "number", "description": "The `duration` represents the duration in milliseconds\nduring which the tooltip is shown when hovering a cell.\nThis overrides the table's `tooltip_duration` property.\nIf set to `None`, the tooltip will not disappear.", "required": false}, "if": {"name": "exact", "value": {"column_id": {"name": "string", "description": "`column_id` refers to the column ID that must be matched.", "required": false}, "filter_query": {"name": "string", "description": "`filter_query` refers to the query that must evaluate to True.", "required": false}, "row_index": {"name": "union", "value": [{"name": "number"}, {"name": "enum", "value": [{"value": "'odd'", "computed": false}, {"value": "'even'", "computed": false}]}], "description": "`row_index` refers to the index of the row in the source `data`.", "required": false}}, "description": "The `if` refers to the condition that needs to be fulfilled\nin order for the associated tooltip configuration to be\nused. If multiple conditions are defined, all conditions\nmust be met for the tooltip to be used by a cell.", "required": true}, "type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'markdown'", "computed": false}], "description": "The `type` refers to the type of tooltip syntax used\nfor the tooltip generation. Can either be `markdown`\nor `text`. Defaults to `text`.", "required": false}, "value": {"name": "string", "description": "The `value` refers to the syntax-based content of the tooltip. This value is required.", "required": true}}}}, "required": false, "description": "`tooltip_conditional` represents the tooltip shown\nfor different columns and cells.\nThis property allows you to specify different tooltips\ndepending on certain conditions. For example, you may have\ndifferent tooltips in the same column based on the value\nof a certain data property.\nPriority is from first to last defined conditional tooltip\nin the list. Higher priority (more specific) conditional\ntooltips should be put at the beginning of the list.", "defaultValue": {"value": "[]", "computed": false}}, "tooltip_data": {"type": {"name": "arrayOf", "value": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "exact", "value": {"delay": {"name": "number", "description": "The `delay` represents the delay in milliseconds before\nthe tooltip is shown when hovering a cell. This overrides\nthe table's `tooltip_delay` property. If set to `None`,\nthe tooltip will be shown immediately.", "required": false}, "duration": {"name": "number", "description": "The `duration` represents the duration in milliseconds\nduring which the tooltip is shown when hovering a cell.\nThis overrides the table's `tooltip_duration` property.\nIf set to `None`, the tooltip will not disappear.\nAlternatively, the value of the property can also be\na plain string. The `text` syntax will be used in\nthat case.", "required": false}, "type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'markdown'", "computed": false}], "description": "For each tooltip configuration,\nThe `type` refers to the type of tooltip syntax used\nfor the tooltip generation. Can either be `markdown`\nor `text`. Defaults to `text`.", "required": false}, "value": {"name": "string", "description": "The `value` refers to the syntax-based content of the tooltip. This value is required.", "required": true}}}]}}}, "required": false, "description": "`tooltip_data` represents the tooltip shown\nfor different columns and cells.\nA list of dicts for which each key is\na column id and the value is a tooltip configuration.", "defaultValue": {"value": "[]", "computed": false}}, "tooltip_header": {"type": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "exact", "value": {"delay": {"name": "number", "description": "The `delay` represents the delay in milliseconds before\nthe tooltip is shown when hovering a cell. This overrides\nthe table's `tooltip_delay` property. If set to `None`,\nthe tooltip will be shown immediately.", "required": false}, "duration": {"name": "number", "description": "The `duration` represents the duration in milliseconds\nduring which the tooltip is shown when hovering a cell.\nThis overrides the table's `tooltip_duration` property.\nIf set to `None`, the tooltip will not disappear.\nAlternatively, the value of the property can also be\na plain string. The `text` syntax will be used in\nthat case.", "required": false}, "type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'markdown'", "computed": false}], "description": "For each tooltip configuration,\nThe `type` refers to the type of tooltip syntax used\nfor the tooltip generation. Can either be `markdown`\nor `text`. Defaults to `text`.", "required": false}, "value": {"name": "string", "description": "The `value` refers to the syntax-based content of the tooltip. This value is required.", "required": true}}}, {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "enum", "value": [{"value": "null", "computed": false}]}, {"name": "string"}, {"name": "exact", "value": {"delay": {"name": "number", "required": false}, "duration": {"name": "number", "required": false}, "type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'markdown'", "computed": false}], "required": false}, "value": {"name": "string", "required": true}}}]}}]}}, "required": false, "description": "`tooltip_header` represents the tooltip shown\nfor each header column and optionally each header row.\nExample to show long column names in a tooltip: {i: i for i in df.columns}.\nExample to show different column names in a tooltip: {'<PERSON>': 'Republican', 'Dem': 'Democrat'}.\nIf the table has multiple rows of headers, then use a list as the value of the\n`tooltip_header` items.", "defaultValue": {"value": "{}", "computed": false}}, "tooltip_delay": {"type": {"name": "number"}, "required": false, "description": "`tooltip_delay` represents the table-wide delay in milliseconds before\nthe tooltip is shown when hovering a cell. If set to `None`, the tooltip\nwill be shown immediately.\nDefaults to 350.", "defaultValue": {"value": "350", "computed": false}}, "tooltip_duration": {"type": {"name": "number"}, "required": false, "description": "`tooltip_duration` represents the table-wide duration in milliseconds\nduring which the tooltip will be displayed when hovering a cell. If\nset to `None`, the tooltip will not disappear.\nDefaults to 2000.", "defaultValue": {"value": "2000", "computed": false}}, "locale_format": {"type": {"name": "exact", "value": {"symbol": {"name": "arrayOf", "value": {"name": "string"}, "description": "(default: ['$', '']). A  list of two strings representing the\n  prefix and suffix symbols. Typically used for currency, and implemented using d3's\n  currency format, but you can use this for other symbols such as measurement units.", "required": false}, "decimal": {"name": "string", "description": "(default: '.'). The string used for the decimal separator.", "required": false}, "group": {"name": "string", "description": "(default: ','). The string used for the groups separator.", "required": false}, "grouping": {"name": "arrayOf", "value": {"name": "number"}, "description": "(default: [3]). A  list of integers representing the grouping pattern.", "required": false}, "numerals": {"name": "arrayOf", "value": {"name": "string"}, "description": "A list of ten strings used as replacements for numbers 0-9.", "required": false}, "percent": {"name": "string", "description": "(default: '%'). The string used for the percentage symbol.", "required": false}, "separate_4digits": {"name": "bool", "description": "(default: True). Separate integers with 4-digits or less.", "required": false}}}, "required": false, "description": "The localization specific formatting information applied to all columns in the table.\nThis prop is derived from the [d3.formatLocale](https://github.com/d3/d3-format#formatLocale) data structure specification.\nWhen left unspecified, each individual nested prop will default to a pre-determined value."}, "style_as_list_view": {"type": {"name": "bool"}, "required": false, "description": "If True, then the table will be styled like a list view\nand not have borders between the columns.", "defaultValue": {"value": "false", "computed": false}}, "fill_width": {"type": {"name": "bool"}, "required": false, "description": "`fill_width` toggles between a set of CSS for two common behaviors:\nTrue: The table container's width will grow to fill the available space;\nFalse: The table container's width will equal the width of its content.", "defaultValue": {"value": "true", "computed": false}}, "markdown_options": {"type": {"name": "exact", "value": {"link_target": {"name": "union", "value": [{"name": "string"}, {"name": "enum", "value": [{"value": "'_blank'", "computed": false}, {"value": "'_parent'", "computed": false}, {"value": "'_self'", "computed": false}, {"value": "'_top'", "computed": false}]}], "description": "(default: '_blank').  The link's behavior (_blank opens the link in a\nnew tab, _parent opens the link in the parent frame, _self opens the link in the\ncurrent tab, and _top opens the link in the top frame) or a string", "required": false}, "html": {"name": "bool", "description": "(default: False)  If True, html may be used in markdown cells\nBe careful enabling html if the content being rendered can come\nfrom an untrusted user, as this may create an XSS vulnerability.", "required": false}}}, "required": false, "description": "The `markdown_options` property allows customization of the markdown cells behavior.", "defaultValue": {"value": "{\n    link_target: '_blank',\n    html: false\n}", "computed": false}}, "css": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"selector": {"name": "string", "required": true}, "rule": {"name": "string", "required": true}}}}, "required": false, "description": "The `css` property is a way to embed CSS selectors and rules\nonto the page.\nWe recommend starting with the `style_*` properties\nbefore using this `css` property.\nExample:\n[\n    {\"selector\": \".dash-spreadsheet\", \"rule\": 'font-family: \"monospace\"'}\n]", "defaultValue": {"value": "[]", "computed": false}}, "style_table": {"type": {"name": "object"}, "required": false, "description": "CSS styles to be applied to the outer `table` container.\nThis is commonly used for setting properties like the\nwidth or the height of the table.", "defaultValue": {"value": "{}", "computed": false}}, "style_cell": {"type": {"name": "object"}, "required": false, "description": "CSS styles to be applied to each individual cell of the table.\nThis includes the header cells, the `data` cells, and the filter\ncells."}, "style_data": {"type": {"name": "object"}, "required": false, "description": "CSS styles to be applied to each individual data cell.\nThat is, unlike `style_cell`, it excludes the header and filter cells."}, "style_filter": {"type": {"name": "object"}, "required": false, "description": "CSS styles to be applied to the filter cells.\nNote that this may change in the future as we build out a\nmore complex filtering UI."}, "style_header": {"type": {"name": "object"}, "required": false, "description": "CSS styles to be applied to each individual header cell.\nThat is, unlike `style_cell`, it excludes the `data` and filter cells."}, "style_cell_conditional": {"type": {"name": "arrayOf", "value": {"name": "shape", "value": {"if": {"name": "exact", "value": {"column_id": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}], "required": false}, "column_type": {"name": "enum", "value": [{"value": "'any'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'text'", "computed": false}, {"value": "'datetime'", "computed": false}], "required": false}}, "required": false}}}}, "required": false, "description": "Conditional CSS styles for the cells.\nThis can be used to apply styles to cells on a per-column basis.", "defaultValue": {"value": "[]", "computed": false}}, "style_data_conditional": {"type": {"name": "arrayOf", "value": {"name": "shape", "value": {"if": {"name": "exact", "value": {"column_id": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}], "required": false}, "column_type": {"name": "enum", "value": [{"value": "'any'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'text'", "computed": false}, {"value": "'datetime'", "computed": false}], "required": false}, "filter_query": {"name": "string", "required": false}, "state": {"name": "enum", "value": [{"value": "'active'", "computed": false}, {"value": "'selected'", "computed": false}], "required": false}, "row_index": {"name": "union", "value": [{"name": "number"}, {"name": "enum", "value": [{"value": "'odd'", "computed": false}, {"value": "'even'", "computed": false}]}, {"name": "arrayOf", "value": {"name": "number"}}], "required": false}, "column_editable": {"name": "bool", "required": false}}, "required": false}}}}, "required": false, "description": "Conditional CSS styles for the data cells.\nThis can be used to apply styles to data cells on a per-column basis.", "defaultValue": {"value": "[]", "computed": false}}, "style_filter_conditional": {"type": {"name": "arrayOf", "value": {"name": "shape", "value": {"if": {"name": "exact", "value": {"column_id": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}], "required": false}, "column_type": {"name": "enum", "value": [{"value": "'any'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'text'", "computed": false}, {"value": "'datetime'", "computed": false}], "required": false}, "column_editable": {"name": "bool", "required": false}}, "required": false}}}}, "required": false, "description": "Conditional CSS styles for the filter cells.\nThis can be used to apply styles to filter cells on a per-column basis.", "defaultValue": {"value": "[]", "computed": false}}, "style_header_conditional": {"type": {"name": "arrayOf", "value": {"name": "shape", "value": {"if": {"name": "exact", "value": {"column_id": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}], "required": false}, "column_type": {"name": "enum", "value": [{"value": "'any'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'text'", "computed": false}, {"value": "'datetime'", "computed": false}], "required": false}, "header_index": {"name": "union", "value": [{"name": "number"}, {"name": "arrayOf", "value": {"name": "number"}}, {"name": "enum", "value": [{"value": "'odd'", "computed": false}, {"value": "'even'", "computed": false}]}], "required": false}, "column_editable": {"name": "bool", "required": false}}, "required": false}}}}, "required": false, "description": "Conditional CSS styles for the header cells.\nThis can be used to apply styles to header cells on a per-column basis.", "defaultValue": {"value": "[]", "computed": false}}, "virtualization": {"type": {"name": "bool"}, "required": false, "description": "This property tells the table to use virtualization when rendering.\nAssumptions are that:\nthe width of the columns is fixed;\nthe height of the rows is always the same; and\nruntime styling changes will not affect width and height vs. first rendering", "defaultValue": {"value": "false", "computed": false}}, "derived_filter_query_structure": {"type": {"name": "object"}, "required": false, "description": "This property represents the current structure of\n`filter_query` as a tree structure. Each node of the\nquery structure has:\ntype (string; required):\n  'open-block',\n  'logical-operator',\n  'relational-operator',\n  'unary-operator', or\n  'expression';\nsubType (string; optional):\n  'open-block': '()',\n  'logical-operator': '&&', '||',\n  'relational-operator': '=', '>=', '>', '<=', '<', '!=', 'contains',\n  'unary-operator': '!', 'is bool', 'is even', 'is nil', 'is num', 'is object', 'is odd', 'is prime', 'is str',\n  'expression': 'value', 'field';\nvalue (any):\n  'expression, value': passed value,\n  'expression, field': the field/prop name.\nblock (nested query structure; optional).\nleft (nested query structure; optional).\nright (nested query structure; optional).\nIf the query is invalid or empty, the `derived_filter_query_structure` will\nbe `None`."}, "derived_viewport_data": {"type": {"name": "arrayOf", "value": {"name": "object"}}, "required": false, "description": "This property represents the current state of `data`\non the current page. This property will be updated\non paging, sorting, and filtering.", "defaultValue": {"value": "[]", "computed": false}}, "derived_viewport_indices": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "`derived_viewport_indices` indicates the order in which the original\nrows appear after being filtered, sorted, and/or paged.\n`derived_viewport_indices` contains indices for the current page,\nwhile `derived_virtual_indices` contains indices across all pages.", "defaultValue": {"value": "[]", "computed": false}}, "derived_viewport_row_ids": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "`derived_viewport_row_ids` lists row IDs in the order they appear\nafter being filtered, sorted, and/or paged.\n`derived_viewport_row_ids` contains IDs for the current page,\nwhile `derived_virtual_row_ids` contains IDs across all pages.", "defaultValue": {"value": "[]", "computed": false}}, "derived_viewport_selected_columns": {"type": {"name": "arrayOf", "value": {"name": "string"}}, "required": false, "description": "`derived_viewport_selected_columns` contains the ids of the\n`selected_columns` that are not currently hidden."}, "derived_viewport_selected_rows": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "`derived_viewport_selected_rows` represents the indices of the\n`selected_rows` from the perspective of the `derived_viewport_indices`.", "defaultValue": {"value": "[]", "computed": false}}, "derived_viewport_selected_row_ids": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "`derived_viewport_selected_row_ids` represents the IDs of the\n`selected_rows` on the currently visible page.", "defaultValue": {"value": "[]", "computed": false}}, "derived_virtual_data": {"type": {"name": "arrayOf", "value": {"name": "object"}}, "required": false, "description": "This property represents the visible state of `data`\nacross all pages after the front-end sorting and filtering\nas been applied.", "defaultValue": {"value": "[]", "computed": false}}, "derived_virtual_indices": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "`derived_virtual_indices` indicates the order in which the original\nrows appear after being filtered and sorted.\n`derived_viewport_indices` contains indices for the current page,\nwhile `derived_virtual_indices` contains indices across all pages.", "defaultValue": {"value": "[]", "computed": false}}, "derived_virtual_row_ids": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "`derived_virtual_row_ids` indicates the row IDs in the order in which\nthey appear after being filtered and sorted.\n`derived_viewport_row_ids` contains IDs for the current page,\nwhile `derived_virtual_row_ids` contains IDs across all pages.", "defaultValue": {"value": "[]", "computed": false}}, "derived_virtual_selected_rows": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "`derived_virtual_selected_rows` represents the indices of the\n `selected_rows` from the perspective of the `derived_virtual_indices`.", "defaultValue": {"value": "[]", "computed": false}}, "derived_virtual_selected_row_ids": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "`derived_virtual_selected_row_ids` represents the IDs of the\n`selected_rows` as they appear after filtering and sorting,\nacross all pages.", "defaultValue": {"value": "[]", "computed": false}}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of the table."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the user makes changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, any `persisted_props` that the\nuser has changed while using the app will keep those changes, as long as\nthe new prop value also matches what was given originally.\nUsed in conjunction with `persistence_type` and `persisted_props`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'columns.name'", "computed": false}, {"value": "'data'", "computed": false}, {"value": "'filter_query'", "computed": false}, {"value": "'hidden_columns'", "computed": false}, {"value": "'page_current'", "computed": false}, {"value": "'selected_columns'", "computed": false}, {"value": "'selected_rows'", "computed": false}, {"value": "'sort_by'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page.", "defaultValue": {"value": "[\n    'columns.name',\n    'filter_query',\n    'hidden_columns',\n    'page_current',\n    'selected_columns',\n    'selected_rows',\n    'sort_by'\n]", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}}